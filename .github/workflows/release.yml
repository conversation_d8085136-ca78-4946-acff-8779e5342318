name: Release Deployment
run-name: Deploy Release ${{ (github.event.release.prerelease == true) && 'Staging' || 'Production' }} - ${{ github.event.release.tag_name }}

on:
  release:
    types: [released]

jobs:
  build_and_push_migrations:
    name: PROD | API migrations (Build & Push)
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image-migrations 
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-api-migrations:$IMAGE_TAG -f ./docker/images/Dockerfile.migrations .
          docker push $ARTIFACT_REGISTRY/goteacher-api-migrations:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-api-migrations:$IMAGE_TAG" >> $GITHUB_OUTPUT

  build_and_push_seeds:
    name: PROD | API seeds (Build & Push)
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image-seeds 
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-api-seeds:$IMAGE_TAG -f ./docker/images/Dockerfile.seeds --target production .
          docker push $ARTIFACT_REGISTRY/goteacher-api-seeds:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-api-seeds:$IMAGE_TAG" >> $GITHUB_OUTPUT

  build_and_push_api:
    name: PROD | API (Build & Push)
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image-api
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-api:$IMAGE_TAG -f ./docker/images/Dockerfile.api --target production .
          docker push $ARTIFACT_REGISTRY/goteacher-api:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-api:$IMAGE_TAG" >> $GITHUB_OUTPUT
  
  build_and_push_export:
    name: PROD | EXPORT (Build & Push)
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image-export
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-export:$IMAGE_TAG -f ./docker/images/Dockerfile.export --target production .
          docker push $ARTIFACT_REGISTRY/goteacher-export:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-export:$IMAGE_TAG" >> $GITHUB_OUTPUT

  build_and_push_ingestion:
    name: PROD | INGESTION (Build & Push)
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image-ingestion 
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-ingestion:$IMAGE_TAG -f ./docker/images/Dockerfile.ingestion --target production .
          docker push $ARTIFACT_REGISTRY/goteacher-ingestion:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-ingestion:$IMAGE_TAG" >> $GITHUB_OUTPUT

  build_and_push_enrichment_worker:
    name: PROD | Enrichment Worker
    runs-on: ubuntu-latest
    if: ${{ github.event.release.prerelease == false }}
    permissions:
      contents: read
      id-token: 'write'
    steps:
      - uses: actions/checkout@v3
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: '${{ secrets.GCP_PROD_SA_KEY }}'

      - name: Authenticate to Google Cloud
        run: gcloud auth configure-docker us-west1-docker.pkg.dev

      - name: Build and push Docker image to Artifact Registry
        id: build-image
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          docker build -t $ARTIFACT_REGISTRY/goteacher-enrichment-worker:$IMAGE_TAG -f ./docker/images/Dockerfile.enrichment_worker --target production .
          docker push $ARTIFACT_REGISTRY/goteacher-enrichment-worker:$IMAGE_TAG
          echo "image=$ARTIFACT_REGISTRY/goteacher-enrichment-worker:$IMAGE_TAG" >> $GITHUB_OUTPUT

  update_k8s_gcp_manifests:
    name: Update k8s-gcp Manifests and Commit
    needs: [build_and_push_migrations, build_and_push_seeds, build_and_push_api, build_and_push_export, build_and_push_ingestion, build_and_push_enrichment_worker]
    runs-on: ubuntu-latest
    permissions:
      contents: write 
    steps:
      - name: Checkout k8s-gcp Repo
        uses: actions/checkout@v3
        with:
          repository: GoTeacher-Inc/k8s-gcp
          token: ${{ secrets.K8S_GCP_REPO_ACCESS_TOKEN }}

      - name: Update Manifests
        env:
          ARTIFACT_REGISTRY: ${{ secrets.GCP_PROD_ARTIFACT_REGISTRY }}
          IMAGE_TAG: prod-${{ github.sha }}
        run: |
          cd prod/backend

          # migrations
          migrations_image_name="$ARTIFACT_REGISTRY/goteacher-api-migrations:$IMAGE_TAG"
          echo "Image name for migrations: $migrations_image_name" 
          sed -i "s|image: .*/goteacher-api-migrations:.*|image: \"$migrations_image_name\"|" ./api/migration-job.yaml

          # seeds
          seeds_image_name="$ARTIFACT_REGISTRY/goteacher-api-seeds:$IMAGE_TAG"
          echo "Image name for seeds: $seeds_image_name"
          sed -i "s|image: .*/goteacher-api-seeds:.*|image: \"$seeds_image_name\"|" ./api/migration-job.yaml

          # api
          api_image_name="$ARTIFACT_REGISTRY/goteacher-api:$IMAGE_TAG"
          echo "Image name for api: $api_image_name"
          sed -i "s|image: .*/goteacher-api:.*|image: \"$api_image_name\"|" ./api/*-deployment.yaml

          # export
          export_image_name="$ARTIFACT_REGISTRY/goteacher-export:$IMAGE_TAG"
          echo "Image name for export: $export_image_name"
          sed -i "s|image: .*/goteacher-export:.*|image: \"$export_image_name\"|" ./export/*-deployment.yaml

          # ingestion
          ingestion_image_name="$ARTIFACT_REGISTRY/goteacher-ingestion:$IMAGE_TAG"
          echo "Image name for ingestion: $ingestion_image_name"
          sed -i "s|image: .*/goteacher-ingestion:.*|image: \"$ingestion_image_name\"|" ./ingestion/*-deployment.yaml
          
          #enrichment-worker
          enrichment_worker_image_name="$ARTIFACT_REGISTRY/goteacher-enrichment-worker:$IMAGE_TAG"
          echo "Image name for enrichment-worker: $enrichment_worker_image_name"
          sed -i "s|image: .*/goteacher-enrichment-worker:.*|image: \"$enrichment_worker_image_name\"|" ./enrichment-worker/*-deployment.yaml          

      - name: Commit and Push Changes
        run: |
          cd prod/backend
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
          git add .
          git commit -m "Update image tags for prod environment"
          git push origin main 