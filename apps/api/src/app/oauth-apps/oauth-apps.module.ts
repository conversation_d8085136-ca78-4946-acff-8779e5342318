import { GetOAuthAppsHand<PERSON>, UpdateOAuthAppDomainHandler } from '@goteacher/app/oauth-apps';
import { MongooseModelModule } from '@goteacher/app/models/mongo';
import { Module } from '@nestjs/common';
import { OAuthAppsController } from 'apps/api/src/api/oauth-apps/oauth-apps.controller';

@Module({
  imports: [MongooseModelModule],
  controllers: [OAuthAppsController],
  providers: [GetOAuthAppsHandler, UpdateOAuthAppDomainHandler],
  exports: [],
})
export class APIOAuthAppsModule {}