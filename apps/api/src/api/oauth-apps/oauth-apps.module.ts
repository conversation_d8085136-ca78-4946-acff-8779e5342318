import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { OAuthAppsController } from './oauth-apps.controller';
import { GetOAuthAppsHandler } from '@goteacher/app/oauth-apps';
import { MongooseModelModule } from '@goteacher/app/models/mongo';

@Module({
  imports: [CqrsModule, MongooseModelModule],
  controllers: [OAuthAppsController],
  providers: [GetOAuthAppsHandler],
})
export class OAuthAppsModule {}