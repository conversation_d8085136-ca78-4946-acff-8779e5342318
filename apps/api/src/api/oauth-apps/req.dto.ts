import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, Matches } from 'class-validator';
import { PaginationRequest } from '@goteacher/app/utility';

export class GetOAuthAppsRequestDto extends PaginationRequest {
  @ApiPropertyOptional({
    description: 'Filter by app name (case-insensitive partial match)',
    example: 'Google',
  })
  @IsOptional()
  @IsString()
  appName?: string;

  @ApiPropertyOptional({
    description: 'Filter by exact client ID',
    example: '123456789.apps.googleusercontent.com',
  })
  @IsOptional()
  @IsString()
  clientId?: string;
}

export class UpdateOAuthAppDomainRequestDto {
  @ApiProperty({
    description: 'Domain name for the OAuth application',
    example: 'google.com',
    required: true,
  })
  @IsString()
  @Matches(
    /^(NOT_FOUND|([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,})$/,
    {
      message: 'Domain must be a valid domain name (e.g., example.com) or "NOT_FOUND"',
    },
  )
  domain: string;
}