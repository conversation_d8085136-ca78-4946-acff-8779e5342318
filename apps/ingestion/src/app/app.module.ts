import { CommonModule } from '@goteacher/app/common/common.module';
import { Module } from '@nestjs/common';
import { IngestionAuthModule } from 'apps/ingestion/src/app/auth/auth.module';
import { IngestionBlacklistModule } from 'apps/ingestion/src/app/blacklist/blacklist.module';
import { IngestionCoreModule } from 'apps/ingestion/src/app/core/core.module';
import { IngestionEventModule } from 'apps/ingestion/src/app/event/event.module';
import { IngestionScrapingModule } from 'apps/ingestion/src/app/scraping/scraping.module';

@Module({
  imports: [
    CommonModule,
    IngestionCoreModule,
    IngestionAuthModule,
    IngestionBlacklistModule,
    IngestionEventModule,
    IngestionScrapingModule,
  ],
  exports: [
    CommonModule,
    IngestionCoreModule,
    IngestionAuthModule,
    IngestionBlacklistModule,
    IngestionEventModule,
    IngestionScrapingModule,
  ],
})
export class IngestionAppModule {}
