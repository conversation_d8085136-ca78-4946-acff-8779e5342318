import { EventType } from '@goteacher/app/event/event.types';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class BaseEventDto {
  @ApiProperty({ description: 'Event ID', required: true, type: String })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType;

  @ApiProperty({ description: 'Domain', required: true, type: String })
  @IsString()
  domain: string;

  @ApiProperty({ description: 'URL', required: true, type: String })
  @IsString()
  url: string;

  @ApiProperty({ description: 'Session ID', required: true, type: String })
  @IsString()
  sessionId: string;

  @ApiProperty({ description: 'Tab ID', required: true, type: Number })
  @IsNumber()
  tabId: number;

  @ApiProperty({
    description: 'Created at unix timestamp',
    required: true,
    type: Number,
  })
  @IsNumber()
  created_at: number;

  @ApiProperty({
    description: 'Client timezone',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  clientTimezone: string;

  @ApiProperty({
    description: 'FavIcon URL',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  favIconUrl?: string;

  @ApiProperty({
    description: 'Browser brand',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  browserBrand?: string;

  @ApiProperty({
    description: 'Browser version',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  browserVersion?: string;

  @ApiProperty({
    description: 'Platform',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({
    description: 'ProductId',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  product?: string;
}

export class ClickEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.CLICK;

  @ApiProperty({ description: 'X position', required: true, type: Number })
  @IsNumber()
  posX: number;

  @ApiProperty({ description: 'Y position', required: true, type: Number })
  @IsNumber()
  posY: number;
}

export class KeydownEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.KEYDOWN;

  @ApiProperty({ description: 'Key', required: true, type: String })
  @IsString()
  key: string;
}

export class ScrollEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.SCROLL;

  @ApiProperty({
    description: 'Scroll percentage',
    required: true,
    type: Number,
  })
  @IsNumber()
  scrollPercentage: number;

  @ApiProperty({ description: 'Document height', required: true, type: Number })
  @IsNumber()
  docHeight: number;

  @ApiProperty({ description: 'Scroll top', required: true, type: Number })
  @IsNumber()
  scrollTop: number;

  @ApiProperty({ description: 'Window height', required: true, type: Number })
  @IsNumber()
  winHeight: number;
}

export class PageOpenEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_OPEN;
}

export class PageCloseEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_CLOSE;
}

export class VideoPlayEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_PLAY;

  @ApiProperty({ description: 'Video time', required: true, type: Number })
  @IsNumber()
  videoTime: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoPauseEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_PAUSE;

  @ApiProperty({ description: 'Video time', required: true, type: Number })
  @IsNumber()
  videoTime: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoSeekEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_SEEK;

  @ApiProperty({ description: 'Video time to', required: true, type: Number })
  @IsNumber()
  videoTimeTo: number;

  @ApiProperty({ description: 'Video time from', required: true, type: Number })
  @IsNumber()
  videoTimeFrom: number;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoEndedEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_ENDED;

  @ApiProperty({ description: 'Video duration', required: true, type: Number })
  @IsNumber()
  videoDuration: number;
}

export class VideoVolumeEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.VIDEO_VOLUME;

  @ApiProperty({ description: 'Video volume', required: true, type: Number })
  @IsNumber()
  videoVolume: number;
}

export class PageScrapingEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.PAGE_SCRAPING;

  @ApiProperty({ description: 'Page contents', required: true, type: Array })
  @IsArray()
  contents: Array<object>;

  @ApiProperty({ description: 'Metadata', required: true, type: Array })
  @IsArray()
  metadata: Array<object>;
}

export class NoActivityEventDto extends BaseEventDto {
  @ApiProperty({ description: 'Event type', required: true, enum: EventType })
  @IsEnum(EventType)
  type: EventType.NO_ACTIVITY;
}

export type CreateEventBodyDto =
  | ClickEventDto
  | KeydownEventDto
  | ScrollEventDto
  | PageOpenEventDto
  | PageCloseEventDto
  | VideoPlayEventDto
  | VideoPauseEventDto
  | VideoSeekEventDto
  | VideoEndedEventDto
  | VideoVolumeEventDto
  | PageScrapingEventDto
  | NoActivityEventDto;
