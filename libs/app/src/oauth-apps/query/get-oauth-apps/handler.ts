import { OAuthApp, OAuthAppDocument } from '@goteacher/app/models/mongo';
import { PaginationResponse, parseOrderByMongoose } from '@goteacher/app/utility';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetOAuthAppsQuery } from './query';

@QueryHandler(GetOAuthAppsQuery)
export class GetOAuthAppsHandler implements IQueryHandler<GetOAuthAppsQuery> {
  constructor(
    @InjectModel(OAuthApp.name)
    private readonly oauthAppModel: Model<OAuthAppDocument>,
  ) {}

  async execute(query: GetOAuthAppsQuery): Promise<PaginationResponse<OAuthApp>> {
    const { organisationId, districtId, pagination, filters } = query;

    const matchStage: any = { organisationId };

    if (filters?.appName) {
      matchStage.appName = { $regex: filters.appName, $options: 'i' };
    }

    if (filters?.clientId) {
      matchStage.clientId = filters.clientId;
    }

    const sort = parseOrderByMongoose(pagination.order || []);

    // Build aggregation pipeline
    const pipeline: any[] = [
      // Match stage
      { $match: matchStage },
      
      // Facet for pagination and total count
      {
        $facet: {
          metadata: [{ $count: "total" }],
          data: [
            ...(Object.keys(sort).length > 0 ? [{ $sort: sort }] : []),
            
            { $skip: pagination.offset },
            { $limit: pagination.limit },
            
            // Lookup privacy policy analysis using the oauth app's domain directly
            {
              $lookup: {
                from: "domains_analysis",
                let: { domain: "$domain" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $ne: ["$$domain", null] },
                          { $eq: [{ $toLower: "$domain" }, { $toLower: "$$domain" }] }
                        ]
                      }
                    }
                  },
                  { $limit: 1 }
                ],
                as: "privacyPolicyAnalysis"
              }
            },
            
            // Unwind privacy policy
            {
              $unwind: {
                path: "$privacyPolicyAnalysis",
                preserveNullAndEmptyArrays: true
              }
            },
            
            // Lookup SDPC agreements with domain matching
            {
              $lookup: {
                from: "sdpc_agreements",
                let: { 
                  domain: "$domain"
                },
                pipeline: [
                  {
                    $match: {
                      $and: [
                        // Filter by district first - this uses an index!
                        { districtid: districtId },
                        // Only active agreements
                        { status: "Active" },
                        // Match by domain extracted from website URL
                        {
                          $expr: {
                            $and: [
                              { $ne: ["$$domain", null] },
                              { $ne: ["$website", null] },
                              {
                                $eq: [
                                  {
                                    $toLower: {
                                      $replaceAll: {
                                        input: {
                                          // Extract domain from URL by removing protocol and path
                                          $arrayElemAt: [
                                            {
                                              $split: [
                                                {
                                                  $replaceAll: {
                                                    input: {
                                                      $replaceAll: {
                                                        input: "$website",
                                                        find: "https://",
                                                        replacement: ""
                                                      }
                                                    },
                                                    find: "http://",
                                                    replacement: ""
                                                  }
                                                },
                                                "/"
                                              ]
                                            },
                                            0
                                          ]
                                        },
                                        find: "www.",
                                        replacement: ""
                                      }
                                    }
                                  },
                                  { $toLower: "$$domain" }
                                ]
                              }
                            ]
                          }
                        }
                      ]
                    }
                  },
                  // Sort by date_expired descending to get latest expiration first
                  { $sort: { date_expired: -1 } },
                  { $limit: 5 } // Get top 5 matching agreements
                ],
                as: "sdpcAgreements"
              }
            },
            
            // Lookup organization domain metadata using domain field
            {
              $lookup: {
                from: "organization_domain_metadata",
                let: { domain: "$domain" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $ne: ["$$domain", null] },
                          { $eq: [{ $toLower: "$domain" }, { $toLower: "$$domain" }] },
                          { $eq: ["$organizationId", organisationId] }
                        ]
                      }
                    }
                  },
                  { $limit: 1 }
                ],
                as: "organizationDomainMetadata"
              }
            },
            
            // Unwind organization domain metadata
            {
              $unwind: {
                path: "$organizationDomainMetadata",
                preserveNullAndEmptyArrays: true
              }
            },
            
            // Add privacy field calculation
            {
              $addFields: {
                privacy: {
                  $cond: {
                    if: {
                      $and: [
                        // Check if we have at least one SDPC agreement
                        { $gt: [{ $size: { $ifNull: ["$sdpcAgreements", []] } }, 0] },
                        // Check if the first agreement has a date_expired field
                        { $ne: [{ $ifNull: [{ $arrayElemAt: ["$sdpcAgreements", 0] }, null] }, null] },
                        // Check if date_expired exists and is not null
                        { $ne: [{ $ifNull: [{ $arrayElemAt: ["$sdpcAgreements.date_expired", 0] }, null] }, null] },
                        // Check if date_expired is greater than current date
                        {
                          $gt: [
                            { $toDate: { $arrayElemAt: ["$sdpcAgreements.date_expired", 0] } },
                            new Date()
                          ]
                        }
                      ]
                    },
                    then: "PASS",
                    else: "FAIL"
                  }
                }
              }
            },
            
            {
              $project: {
                __v: 0,
                _id: 0
              }
            }
          ]
        }
      }
    ];

    const [result] = await this.oauthAppModel.aggregate(pipeline).exec();
    
    const total = result.metadata[0]?.total || 0;
    const data = result.data || [];

    return {
      data,
      total,
      limit: pagination.limit,
      offset: pagination.offset,
    };
  }
}