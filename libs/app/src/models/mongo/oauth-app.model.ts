import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ApiProperty } from '@nestjs/swagger';
import { HydratedDocument } from 'mongoose';

export type OAuthAppDocument = HydratedDocument<OAuthApp>;

@Schema({
  collection: 'oauth_apps',
  timestamps: true,
  autoCreate: true,
  autoIndex: true,
})
export class OAuthApp {
  @ApiProperty({
    description: 'Organisation ID from PostgreSQL',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  @Prop({
    required: true,
    type: String,
    index: true,
  })
  organisationId: string;

  @ApiProperty({
    description: 'OAuth application name',
    example: 'Google Drive',
    required: true,
  })
  @Prop({
    required: true,
    type: String,
  })
  appName: string;

  @ApiProperty({
    description: 'OAuth client ID',
    example: '123456789.apps.googleusercontent.com',
    required: true,
  })
  @Prop({
    required: true,
    type: String,
    index: true,
  })
  clientId: string;

  @ApiProperty({
    description: 'Number of users who have authorized this app',
    example: 150,
    required: true,
  })
  @Prop({
    required: true,
    type: Number,
    default: 0,
  })
  userCount: number;

  @ApiProperty({
    description: 'OAuth scopes requested by the app',
    example: ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/calendar'],
    required: false,
  })
  @Prop({
    type: [String],
    default: [],
  })
  scopes: string[];

  @ApiProperty({
    description: 'Last time the app was accessed',
    example: '2024-01-15T14:30:00Z',
    required: false,
  })
  @Prop({
    type: Date,
    default: null,
  })
  lastAccessed: Date;

  @ApiProperty({
    description: 'Domain name of the OAuth application',
    example: 'google.com',
    required: false,
  })
  @Prop({
    type: String,
    default: null,
  })
  domain: string;
}

export const OAuthAppSchema = SchemaFactory.createForClass(OAuthApp);

// Create compound index for unique constraint on organisationId + clientId
OAuthAppSchema.index(
  { organisationId: 1, clientId: 1 },
  { unique: true, name: 'idx_organisation_client_unique' }
);

// Additional indexes for performance
OAuthAppSchema.index({ lastAccessed: -1 }, { name: 'idx_last_accessed' });
OAuthAppSchema.index({ domain: 1 }, { name: 'idx_domain' });
OAuthAppSchema.index({ userCount: -1 }, { name: 'idx_user_count' });
OAuthAppSchema.index({ organisationId: 1, appName: 1 }, { name: 'idx_organisation_app_name' });