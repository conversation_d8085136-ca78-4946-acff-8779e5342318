import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { UserGroup } from '@goteacher/app/analytics';
import { CalculatedTerm } from '@goteacher/app/financial/financial.types';
import {
  AudienceType,
  Contract,
  ContractDocument,
  LicensableGrades,
  PaymentMode,
  PaymentTermFixedFee,
  PaymentTermLicence,
} from '@goteacher/app/models/mongo';
import { Grade } from '@goteacher/app/models/sequelize';
import {
  School,
  SchoolStatus,
} from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { getAcademicYear, normalizeDateRange } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { InjectModel } from '@nestjs/sequelize';
import { Model } from 'mongoose';
import { Op, OrderItem, QueryTypes } from 'sequelize';

@Injectable()
export class FinancialService {
  private readonly logger = new Logger(FinancialService.name);
  constructor(
    private readonly clickhouse: NodeClickHouseClient,
    @InjectModelMongoose(Contract.name)
    private readonly contractModel: Model<Contract>,
    @InjectModel(School) private readonly schoolModel: typeof School,
    @InjectModel(Grade) private readonly gradeModel: typeof Grade,
    @InjectModel(UserSchool)
    private readonly userSchoolModel: typeof UserSchool,
    @InjectModel(User) private userModel: typeof User,
    private cacheService: ICacheService,
  ) { }

  async calculateContract(contract: ContractDocument) {
    const cachingKey = `financial_${contract.organizationId}_calculateContract_${contract.id}`;
    const cacheResult = await this.cacheService.get<{
      data: {
        contract: ContractDocument;
        terms: CalculatedTerm[];
      };
    }>(cachingKey);
    if (cacheResult) {
      return cacheResult.data;
    }

    const calculatedTerms = await Promise.all(
      contract.terms.map((term) => this.calculateTerm(contract, term)),
    );

    const result = {
      ...contract.toObject(),
      terms: calculatedTerms,
    };

    await this.cacheService.set(cachingKey, { data: result }, 60 * 60 * 24);

    return result;
  }

  async calculateAudience(args: {
    orgId: string;
    schoolIds?: string[];
    grades?: LicensableGrades[];
    userGroup?: UserGroup;
  }) {
    const sql = `
      SELECT 
        count(*) as total,
        countIf(grade == 'KG') as unTrackable,
        countIf(grade != 'KG') as trackable
      FROM users u
      WHERE 
        u.orgId = '${args.orgId}'
        ${args.schoolIds?.length ? `AND u.schoolId IN (${args.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
        ${args.grades?.length ? `AND u.grade IN (${args.grades.map((s) => `'${s}'`).join(',')})` : ''} 
        ${args.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
        ${args.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
      ;
    `;

    this.logger.debug(sql);

    const queryResult = await this.clickhouse.query({
      query: sql,
      format: 'JSON',
    });

    const { data } = await queryResult.json<{
      total: number;
      unTrackable: number;
      trackable: number;
    }>();

    return data.length ? data[0] : { total: 0, unTrackable: 0, trackable: 0 };
  }

  async calculateTerm(
    contract: Contract,
    term: PaymentTermFixedFee | PaymentTermLicence,
  ): Promise<CalculatedTerm> {
    const enrichedTerm = await this.enrichTerm(contract, term);
    const contractUtilization = await this.calculateUtilization(contract, term);

    return {
      ...enrichedTerm,
      costBreakdown: {
        ...this.calculateLicenceCost(
          contract,
          term as PaymentTermLicence,
          contractUtilization,
        ),
        userTotal: contractUtilization.total_users,
      },
    };
  }

  calculateLicenceCost(
    contract: Contract,
    term: PaymentTermLicence | PaymentTermFixedFee,
    contractUtilization: {
      total_users: number;
      active_users: number;
      decommissioned_users: number;
      no_login: number;
    },
  ) {
    const days = Math.round(
      (contract.subscriptionEndDate.getTime() -
        contract.subscriptionStartDate.getTime()) /
      (1000 * 60 * 60 * 24),
    );

    if (term.paymentMode === PaymentMode.FIXED_FEE && !term.audience) {
      const subscriptionCost = (term as PaymentTermFixedFee).totalCost;
      const additionalFees = contract.extraFees;
      const totalCost = subscriptionCost + additionalFees;

      const costPerHour = totalCost / (days * 24);
      return {
        costPerHour,
        subscriptionCost,
        totalCost,
      };
    }

    const licenses =
      (term as PaymentTermLicence).numberOfLicenses ||
      contractUtilization.total_users;
    const costPerLicense =
      (term as PaymentTermLicence).pricePerLicense ||
      Number(((term as PaymentTermFixedFee).totalCost / licenses).toFixed(2));
    const subscriptionCost =
      term.paymentMode === PaymentMode.FIXED_FEE
        ? (term as PaymentTermFixedFee).totalCost
        : licenses * costPerLicense;
    const additionalFees = contract.extraFees;
    const totalCost = subscriptionCost + additionalFees;
    const costPerHour = totalCost / (days * 24);

    const extraLicenses =
      licenses > contractUtilization.total_users
        ? licenses - contractUtilization.total_users
        : 0;
    const activeLicenses = Math.min(contractUtilization.active_users, licenses);
    const inactiveLicenses = Math.max(
      0,
      licenses - contractUtilization.active_users - extraLicenses,
    );

    const utilisation = activeLicenses / licenses;
    const utilisationPercentage = utilisation * 100;

    const costPerUser = totalCost / licenses;
    const costPerActiveUser =
      activeLicenses > 0 ? totalCost / activeLicenses : undefined;

    const costSavings = costPerUser * inactiveLicenses;
    const costSavingsPercentage = (costSavings / totalCost) * 100;
    const roi = 100 - costSavingsPercentage;

    return {
      licenses,
      activeLicenses,
      inactiveLicenses,
      extraLicenses,
      totalCost,
      costPerHour,
      costPerLicense,
      subscriptionCost,
      costPerUser,
      costPerActiveUser,
      costSavings,
      costSavingsPercentage,
      roi,
      utilisation,
      utilisationPercentage,
      decommissionedUsers: contractUtilization.decommissioned_users,
      noLoginUsers: contractUtilization.no_login,
    };
  }

  async enrichTerm(
    contract: Contract,
    term: PaymentTermFixedFee | PaymentTermLicence,
  ) {
    if (!term.audience && term.paymentMode === PaymentMode.FIXED_FEE) {
      return term;
    }

    const contractSchoolIds = term.audience?.schools;
    const contractGrades = term.audience?.grades;

    let filterSchools: any = {
      organisationId: contract.organizationId,
    };

    let filterGrades: any = {
      licenseable: true,
    };

    if (contractSchoolIds && contractSchoolIds.length) {
      filterSchools = {
        ...filterSchools,
        id: {
          [Op.in]: contractSchoolIds,
        },
      };
    }

    if (contractGrades && contractGrades.length) {
      filterGrades = {
        ...filterGrades,
        value: {
          [Op.in]: contractGrades,
        },
      };
    }

    const schools = await this.schoolModel.findAll({
      where: filterSchools,
      attributes: ['id', 'displayName'],
    });

    const grades = await this.gradeModel.findAll({
      where: filterGrades,
      attributes: ['id', 'name', 'value'],
    });

    if (term.audience?.schools.length === 0) {
      term.audience.schools = schools.map((s) => s.id);
    }

    if (term.audience?.grades.length === 0) {
      term.audience.grades = grades.map((g) => g.value as LicensableGrades);
    }

    return { ...term, schools, grades };
  }

  async calculateUtilization(
    contract: Contract,
    term: PaymentTermFixedFee | PaymentTermLicence,
  ) {
    const audience = term.audience;
    const { fromDate, toDate } = normalizeDateRange(
      contract.subscriptionStartDate,
      contract.subscriptionEndDate,
    );
    const { organizationId, domain, productId } = contract;
    const grades: any[] = term.audience?.grades || [];
    if (
      (audience?.type === AudienceType.BOTH ||
        audience?.type === AudienceType.TEACHER) &&
      grades.length > 0
    ) {
      grades.push('N/A');
    }

    const sql = `
      WITH total_users_cte as (
        SELECT
          count(*) as total_users
        FROM
          goteacher.users u
        WHERE
          u.orgId = '${organizationId}'        
          ${audience?.schools.length ? `AND u.schoolId IN (${audience?.schools.map((s) => `'${s}'`).join(',')})` : ''}
          ${audience?.grades.length ? `AND u.grade IN (${audience?.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${audience?.type === AudienceType.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${audience?.type === AudienceType.STUDENT ? `AND u.role = 'student'` : ''}
          ${audience?.type === AudienceType.BOTH ? `AND u.role IN ('student', 'teacher')` : ''}
      ), active_users_cte as (
        SELECT
          count(DISTINCT act.userId) as active_users,
          countIf(DISTINCT act.userId, u.decommissionedAt IS NOT NULL) as decommissioned_users   
        FROM
          goteacher.activity_sessions as act
        INNER JOIN goteacher.users u ON act.userId = u.userId
        WHERE
          u.orgId = '${organizationId}'
          ${productId ? `AND act.productId = '${productId}'` : `AND act.domain = '${domain}'`}
          ${audience?.schools.length ? `AND u.schoolId IN (${audience?.schools.map((s) => `'${s}'`).join(',')})` : ''}
          ${audience?.grades.length ? `AND u.grade IN (${audience?.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${audience?.type === AudienceType.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${audience?.type === AudienceType.STUDENT ? `AND u.role = 'student'` : ''}
          ${audience?.type === AudienceType.BOTH ? `AND u.role IN ('student', 'teacher')` : ''}
          AND act.startTime >= ${Math.round(fromDate.getTime() / 1000)}
          AND act.startTime <= ${Math.round(toDate.getTime() / 1000)}
      )
      SELECT
        tuc.total_users as total_users,
        auc.active_users as active_users,
        auc.decommissioned_users as decommissioned_users,
        total_users - active_users as no_login        
      FROM active_users_cte as auc
      CROSS JOIN total_users_cte as tuc
    `;

    this.logger.debug(`calculateUtilization: ${sql}`);

    const queryResult = await this.clickhouse.query({
      query: sql,
      format: 'JSON',
    });

    const { data } = await queryResult.json<{
      total_users: string;
      active_users: string;
      decommissioned_users: string;
      no_login: string;
    }>();

    const formattedData = data.map((d) => ({
      ...d,
      total_users: Number(d.total_users),
      active_users: Number(d.active_users),
      decommissioned_users: Number(d.decommissioned_users),
      no_login: Number(d.no_login),
    }));

    return formattedData[0];
  }

  async countTotalUsersPerSchoolPerGrade(orgId: string) {
    const cachingKey = `financial_${orgId}_countTotalUsersPerSchoolPerGrade`;
    const cacheResult = await this.cacheService.get<{
      data: { schoolId: string; grade: string; total: number }[];
    }>(cachingKey);
    if (cacheResult) {
      return cacheResult.data;
    }

    const sql = `
      SELECT
          schoolId,
          grade,
          count(*) as total
      FROM
          users
      WHERE
          role = 'student'
          AND grade != 'N/A'
          AND orgId = '${orgId}'
      GROUP BY
          schoolId,
          grade
      
      UNION ALL

      SELECT
          schoolId,
          'teacher' as grade,
          count(*) as total
      FROM
          users
      WHERE
          role IN ('teacher', 'administrator')
          and orgId = '${orgId}'
      GROUP BY
          schoolId
    ;`;

    const orgWithSchoolYear = ['cc5b397a-0e70-4bca-97f5-193ea80a2034'];
    const schollYearCondition = orgWithSchoolYear.includes(orgId)
      ? `AND "UserSchool"."schoolYear" = :actualSchoolYear`
      : '';

    const actualSchoolYear = getAcademicYear(new Date());

    // this.logger.debug(sql);

    // const queryResult = await this.clickhouse.query({
    //   query: sql,
    //   format: 'JSON',
    // });

    // const { data, rows_before_limit_at_least } = await queryResult.json<{
    //   schoolId: string;
    //   grade: string;
    //   total: number;
    // }>();

    const results = await this.userSchoolModel.sequelize.query(
      `
      (
        SELECT
          "UserSchool"."schoolId",
          "UserSchool"."grade",
          COUNT(*) as total
        FROM "userSchool" as "UserSchool"
        INNER JOIN "school" as "School"
          ON "UserSchool"."schoolId" = "School"."id"
        INNER JOIN "users"
          ON "UserSchool"."userId" = "users"."id"
        WHERE
          "School"."organisationId" = :orgId          
          AND "UserSchool"."grade" != 'N/A'
          ${schollYearCondition}
          AND "users"."deleted" = false
        GROUP BY
          "UserSchool"."schoolId",
          "UserSchool"."grade"
      )
      UNION ALL
      (
        SELECT
          "UserSchool"."schoolId",
          'teacher' as grade,
          COUNT(*) as total
        FROM "userSchool" as "UserSchool"
        INNER JOIN "school" as "School"
          ON "UserSchool"."schoolId" = "School"."id"
        INNER JOIN "users"
          ON "UserSchool"."userId" = "users"."id"          
        WHERE
          "School"."organisationId" = :orgId
          AND "users"."role" = 'teacher'
          AND "users"."deleted" = false
          ${schollYearCondition}
        GROUP BY
          "UserSchool"."schoolId"
      )
    `,
      {
        replacements: { orgId, actualSchoolYear },
        type: QueryTypes.SELECT,
      },
    );

    const data = results.length
      ? results.map((dp) => ({
        schoolId: dp['schoolId'],
        grade: dp['grade'],
        total: Number(dp['total']),
      }))
      : [];

    await this.cacheService.set(cachingKey, { data }, 60 * 60 * 24);

    return data;
  }

  async getTopSpendingSchools(orgId: string, fromDate?: Date, toDate?: Date) {
    const schools = await this.schoolModel.findAll({
      where: {
        organisationId: orgId,
        status: SchoolStatus.APPROVED,
      },
      attributes: ['id', 'displayName'],
    });

    // Note: organizationId with "z"
    const whereClause: any = {
      organizationId: orgId,
      active: true,
      deleted: false,
    };

    if (fromDate || toDate) {
      delete whereClause.active;
      // Changed filter logic to intersect with subscriptionStartDate and subscriptionEndDate
      whereClause.$and = [
        {
          $and: [
            {
              subscriptionStartDate: { $lt: toDate || new Date('9999-12-31') },
            },
            {
              subscriptionEndDate: { $gt: fromDate || new Date('1970-01-01') },
            },
          ],
        },
      ];
    }

    const contracts = await this.contractModel.find(whereClause);
    const totalCost = contracts.reduce(
      (acc, curr) =>
        acc +
        curr.terms.reduce((acc2, curr2) => {
          if (curr2.paymentMode === PaymentMode.FIXED_FEE)
            return acc2 + (curr2 as PaymentTermFixedFee).totalCost;
          else
            return (
              acc2 +
              (curr2 as PaymentTermLicence).numberOfLicenses *
              (curr2 as PaymentTermLicence).pricePerLicense
            );
        }, 0),
      0,
    );
    const countTotalUsersPerSchoolPerGradeData =
      await this.countTotalUsersPerSchoolPerGrade(orgId);

    const schoolsTotalCost = new Map<string, number>();
    for (const contract of contracts) {
      for (const term of contract.terms) {
        const termTotalCost =
          term.paymentMode === PaymentMode.FIXED_FEE
            ? (term as PaymentTermFixedFee).totalCost
            : (term as PaymentTermLicence).numberOfLicenses *
            (term as PaymentTermLicence).pricePerLicense;
        const schoolIds = term.audience?.schools.length
          ? term.audience.schools
          : schools.map((s) => s.id);
        const totalAudienceSize = countTotalUsersPerSchoolPerGradeData
          .filter((dp) => {
            const isIncludedSchool = schoolIds.includes(dp.schoolId);
            const isIncludedGradeType =
              term.audience?.type === AudienceType.BOTH
                ? true
                : term.audience?.type === AudienceType.TEACHER
                  ? dp.grade === 'teacher'
                  : dp.grade !== 'teacher';
            const isIncludedGrade = !term.audience?.grades.length
              ? true
              : term.audience?.grades.includes(dp.grade as LicensableGrades);
            return isIncludedSchool && isIncludedGradeType && isIncludedGrade;
          })
          .reduce((acc, curr) => acc + curr.total, 0);

        for (const schoolId of schoolIds) {
          if (totalAudienceSize === 0) continue;

          const school = schools.find((s) => s.id === schoolId);
          const currentTotalCost =
            schoolsTotalCost.get(school.displayName) ?? 0;
          const schoolAudience = countTotalUsersPerSchoolPerGradeData
            .filter((dp) => {
              const isIncludedSchool = dp.schoolId === schoolId;
              const isIncludedGradeType =
                term.audience?.type === AudienceType.BOTH
                  ? true
                  : term.audience?.type === AudienceType.TEACHER
                    ? dp.grade === 'teacher'
                    : dp.grade !== 'teacher';
              const isIncludedGrade = !term.audience?.grades.length
                ? true
                : term.audience?.grades.includes(dp.grade as LicensableGrades);
              return isIncludedSchool && isIncludedGradeType && isIncludedGrade;
            })
            .reduce((acc, curr) => acc + curr.total, 0);
          const schoolCost =
            (schoolAudience / totalAudienceSize) * termTotalCost;
          schoolsTotalCost.set(
            school.displayName,
            currentTotalCost + schoolCost,
          );
        }
      }
    }

    return Array.from(schoolsTotalCost.entries())
      .map(([schoolName, schoolCost]) => {
        return {
          school: schoolName,
          totalCost: schoolCost || 0,
          percentage: ((schoolCost || 0) / totalCost) * 100,
        };
      })
      .sort((a, b) => b.totalCost - a.totalCost);
  }

  async getTopSpendingGrades(orgId: string, fromDate?: Date, toDate?: Date) {
    const grades = await this.gradeModel.findAll({
      where: {
        licenseable: true,
      },
      attributes: ['id', 'name', 'value'],
    });
    const schools = await this.schoolModel.findAll({
      where: {
        organisationId: orgId,
      },
      attributes: ['id', 'displayName'],
    });

    const whereClause: any = {
      organisationId: orgId,
      // active: true,
      deleted: false,
    };
    if (fromDate || toDate) {
      whereClause.$and = [
        {
          $and: [
            {
              subscriptionStartDate: { $lt: toDate || new Date('9999-12-31') },
            },
            {
              subscriptionEndDate: { $gt: fromDate || new Date('1970-01-01') },
            },
          ],
        },
      ];
    }

    const contracts = await this.contractModel.find(whereClause);
    const totalCost = contracts.reduce(
      (acc, curr) =>
        acc +
        curr.terms.reduce((acc2, curr2) => {
          if (curr2.paymentMode === PaymentMode.FIXED_FEE)
            return acc2 + (curr2 as PaymentTermFixedFee).totalCost;
          else
            return (
              acc2 +
              (curr2 as PaymentTermLicence).numberOfLicenses *
              (curr2 as PaymentTermLicence).pricePerLicense
            );
        }, 0),
      0,
    );
    const countTotalUsersPerSchoolPerGradeData =
      await this.countTotalUsersPerSchoolPerGrade(orgId);

    const gradesTotalCost = new Map<string, number>();
    for (const contract of contracts) {
      for (const term of contract.terms) {
        const termTotalCost =
          term.paymentMode === PaymentMode.FIXED_FEE
            ? (term as PaymentTermFixedFee).totalCost
            : (term as PaymentTermLicence).numberOfLicenses *
            (term as PaymentTermLicence).pricePerLicense;
        const schoolIds = term.audience?.schools.length
          ? term.audience.schools
          : schools.map((s) => s.id);
        const gradesValues = term.audience?.grades.length
          ? term.audience.grades
          : grades.map((s) => s.value);
        const totalAudienceSize = countTotalUsersPerSchoolPerGradeData
          .filter((dp) => {
            const isIncludedSchool = schoolIds.includes(dp.schoolId);
            const isIncludedGradeType =
              term.audience?.type === AudienceType.BOTH
                ? true
                : term.audience?.type === AudienceType.TEACHER
                  ? dp.grade === 'teacher'
                  : dp.grade !== 'teacher';
            const isIncludedGrade = !term.audience?.grades.length
              ? true
              : term.audience?.grades.includes(dp.grade as LicensableGrades);
            return isIncludedSchool && isIncludedGradeType && isIncludedGrade;
          })
          .reduce((acc, curr) => acc + curr.total, 0);

        for (const grade of gradesValues) {
          if (totalAudienceSize === 0) continue;

          const currentTotalCost = gradesTotalCost.get(grade) ?? 0;
          const gradeAudience = countTotalUsersPerSchoolPerGradeData
            .filter((dp) => {
              const isIncludedSchool = schoolIds.includes(dp.schoolId);
              const isIncludedGradeType =
                term.audience?.type === AudienceType.BOTH
                  ? true
                  : term.audience?.type === AudienceType.TEACHER
                    ? dp.grade === 'teacher'
                    : dp.grade !== 'teacher';
              const isIncludedGrade = grade === dp.grade;
              return isIncludedSchool && isIncludedGradeType && isIncludedGrade;
            })
            .reduce((acc, curr) => acc + curr.total, 0);
          const gradeCost = (gradeAudience / totalAudienceSize) * termTotalCost;
          gradesTotalCost.set(grade, currentTotalCost + gradeCost);
        }
      }
    }

    return Array.from(gradesTotalCost.entries()).map(([grade, gradeCost]) => {
      return {
        grade,
        totalCost: gradeCost || 0,
        percentage: ((gradeCost || 0) / totalCost) * 100,
      };
    });
  }

  async getContractUsers(
    contract: Contract,
    organisationIds: string[],
    orderBy?: OrderItem[],
    search?: string,
    limit?: number,
    offset?: number,
  ) {
    const {
      domain,
      productId,
      subscriptionStartDate,
      subscriptionEndDate,
      terms: [term],
    } = contract;
    const { audience } = term;

    const searchableUserIds = await this.getSearchableUserIds(
      organisationIds,
      search,
    );

    const sqlQuery = `
    WITH visits_info_cte as (
      ${this.getContractVisitsSQL(contract, organisationIds, searchableUserIds)}
    ), session_metrics AS (
      SELECT 
        sessionId,
        userId,
        schoolId,
        orgId,
        grade,
        role,
        ${productId ? 'productId,' : 'domain,'}
        sumOrDefault(time_spent) AS time_spent                   
      FROM visits_info_cte
      GROUP BY
        sessionId,
        userId,
        schoolId,
        orgId,
        grade,           
        ${productId ? 'productId,' : 'domain,'}
        role        
      ),daily_sessions_cte AS (
      ${this.getContractDailySessionsSQL(contract)}
    )
    SELECT 
      metrics.userId as userId, 
      metrics.page_views as page_views,
      sessions.count_sessions as count_sessions,
      sessions.avg_time_spent as avg_time_spent,
      sessions.sum_time_spent as sum_time_spent      
    FROM 
      (
        SELECT           
          ds.userId as userId,
          avg(avg_time_spent) as avg_time_spent, 
          sum(sum_time_spent) as sum_time_spent,
          uniqMergeOrDefault(count_sessions) as count_sessions
        FROM daily_sessions_cte ds    
        GROUP BY ds.userId
      ) sessions
      LEFT JOIN 
      (
        SELECT           
          admps.userId as userId,
          countMergeOrDefault(page_views) as page_views
        FROM 
          ${productId ? 'weekly_product_metrics_per_user_sc' : 'weekly_domain_metrics_per_user_sc'} admps         
        WHERE 
          ${productId ? `admps.productId = '${productId}'` : `admps.domain = '${domain}'`} 
          AND admps.orgId IN (${organisationIds.map((o) => `'${o}'`).join(',')})
          ${audience?.schools.length
        ? `AND admps.schoolId IN (${audience?.schools.map((s) => `'${s}'`).join(',')})`
        : ''
      }
          AND admps.day >= ${Math.round(new Date(subscriptionStartDate).getTime() / 1000)}
          AND admps.day <= ${Math.round(new Date(subscriptionEndDate).getTime() / 1000)}
        GROUP BY admps.userId
      ) metrics ON sessions.userId = metrics.userId    
    ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
    ${limit ? `LIMIT ${limit}` : ''}
    ${offset ? `OFFSET ${offset}` : ''}
    `;

    this.logger.debug(sqlQuery);

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    return await queryResult.json();
  }

  async getSearchableUserIds(
    orgIds: string[],
    search: string,
  ): Promise<string[]> {
    if (!search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

  getContractVisitsSQL(
    contract: Contract,
    orgIds: string[],
    searchableUserIds: string[],
  ): string {
    const orgId = orgIds[0];
    const {
      domain,
      productId,
      subscriptionStartDate,
      subscriptionEndDate,
      terms: [term],
    } = contract;
    const { audience } = term;
    return `
      SELECT 
        st.sessionId AS sessionId,
        st.tabId AS tabId,
        st.userId AS userId,
        COALESCE(u.orgId, st.orgId) AS orgId,
        COALESCE(u.schoolId, st.schoolId) AS schoolId,
        COALESCE(u.role, st.role) AS role,
        COALESCE(u.grade, st.grade) AS grade,
        ${productId ? 'st.productId AS productId,' : 'st.domain AS domain,'}
        st.url AS url,
        min(st.visit_start) AS visit_start,
        max(st.visit_end) AS visit_end,
        any(st.visit_timezone) AS visit_timezone,
        dateDiff('s', visit_start, visit_end) AS time_spent        
      FROM goteacher.visits_sc as st
      INNER JOIN goteacher.users u on u.userId = st.userId
      WHERE 
        orgId = '${orgId}'
        ${productId ? `AND st.productId = '${productId}'` : `AND st.domain = '${domain}'`}
        ${audience?.schools.length ? `AND schoolId IN (${audience?.schools.map((s) => `'${s}'`).join(',')})` : ''}
        ${audience?.grades.length ? `AND grade IN (${audience?.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${audience?.type === AudienceType.TEACHER ? `AND role = 'teacher'` : ''}
        ${audience?.type === AudienceType.STUDENT ? `AND role = 'student'` : ''}
        AND toDateTime(toStartOfWeek(st.visit_start)) >= ${Math.round(new Date(subscriptionStartDate).getTime() / 1000)}
        AND toDateTime(toStartOfWeek(st.visit_start)) <= ${Math.round(new Date(subscriptionEndDate).getTime() / 1000)}            
        ${searchableUserIds.length ? `AND st.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY
          sessionId,
          tabId,
          userId,
          orgId,
          schoolId,
          role,
          grade,
          ${productId ? 'productId,' : 'domain,'}
          url             
     `;
  }

  getContractDailySessionsSQL(contract: Contract): string {
    const {
      domain,
      productId,
      subscriptionStartDate,
      subscriptionEndDate,
      terms: [term],
    } = contract;
    const { audience } = term;
    return `
      SELECT 
        userId,
        schoolId,
        orgId,
        grade,        
        ${productId ? 'productId,' : 'domain,'}        
        avgOrNull(time_spent) AS avg_time_spent,
        sumOrDefault(time_spent) AS sum_time_spent,
        uniqState(sessionId) AS count_sessions
    FROM session_metrics
    GROUP BY
        userId,
        ${productId ? 'productId,' : 'domain,'}
        schoolId,
        orgId,
        grade        
    `;
  }
}
