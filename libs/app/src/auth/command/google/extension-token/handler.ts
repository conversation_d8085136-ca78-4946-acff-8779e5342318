import { GoogleExtensionTokenCommand } from '@goteacher/app/auth/command/google/extension-token/command';
import { JW<PERSON>rovider } from '@goteacher/app/auth/guard';
import { JWTService } from '@goteacher/app/auth/service';
import {
  Account,
  AccountPlatform,
} from '@goteacher/app/models/sequelize/account.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { toLower } from 'lodash';

@CommandHandler(GoogleExtensionTokenCommand)
export class GoogleExtensionTokenHandler
  implements ICommandHandler<GoogleExtensionTokenCommand> {
  private readonly logger = new Logger(GoogleExtensionTokenHandler.name);

  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Account) private accountModel: typeof Account,
    private cacheService: ICacheService,
    private readonly jwtService: JWTService,
  ) { }

  async execute(
    command: GoogleExtensionTokenCommand,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const OAuthClient = new OAuth2Client();
    OAuthClient.setCredentials({
      access_token: command.token,
    });

    const oauth2 = google.oauth2({
      auth: OAuthClient,
      version: 'v2',
    });

    const response = await oauth2.userinfo.get();

    // We're purposely not validating the organisation here
    // as there have been multiple cases where users get imported after enabling the extension
    let user = await this.userModel.findOne({
      where: { email: toLower(response.data?.email), decommissionedAt: null },
      include: [
        {
          model: UserSchool,
          include: [
            {
              model: School,
              attributes: ['id', 'organisationId'],
            },
          ],
        },
      ],
    });

    if (!user) {
      user = await this.userModel.create({
        email: toLower(response.data?.email),
        firstName: response.data?.given_name,
        lastName: response.data?.family_name,
        picture: response.data?.picture,
        isRegistrationCompleted: false,
      });

      await this.accountModel.create({
        platform: AccountPlatform.GOOGLE_CLASSROOM,
        credentials: {},
        externalId: response.data?.id,
        userId: user.id,
      });
    }

    const schoolIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.schoolId)
      : [];
    const orgIds = user.UserSchool
      ? user.UserSchool.map((userSchool) => userSchool.school.organisationId)
      : [];

    const role = user.role || 'unknown';
    const grade = role === 'student' ? user.UserSchool?.[0]?.grade || "unknown" : "N/A";

    this.cacheService.set(`user-${user.id}`, user, 60 * 60);

    const tokens = this.jwtService.signPayload({
      email: user.email,
      provider: JWTProvider.GOOGLE,
      sub: user.id,
      schoolIds,
      orgIds,
      grade,
      role,
    });

    return tokens;
  }
}
