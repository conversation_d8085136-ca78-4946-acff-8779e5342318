// THIS IS RETARDED can't change now though
export enum EventName {
  // Page
  PAGE_LOAD = 'PAGE_LOAD',
  PAGE_OPEN = 'PAGE_OPEN',
  PAGE_CLOSE = 'PAGE_CLOSE',
  PAGE_CLICK = 'PAGE_CLICK',
  PAGE_SCROLL = 'PAGE_SCROLL',
  PAGE_SCRAPING = 'PAGE_SCRAPING',

  // Media
  MEDIA_START = 'MEDIA_START',
  MEDIA_STOP = 'MEDIA_STOP',
  MEDIA_PAUSE = 'MEDIA_PAUSE',
  MEDIA_VOLUME_CHANGE = 'MEDIA_VOLUME_CHANGE',
  MEDIA_SPEED_CHANGE = 'MEDIA_SPEED_CHANGE',
  MEDIA_SEEK_CHANGE = 'MEDIA_SEEK_CHANGE',

  // Session
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_START = 'SESSION_START',

  // Other
  TEXT_CHANGE = 'TEXT_CHANGE',
  MOUSE_MOVEMENT = 'MOUSE_MOVEMENT',
}


export enum EventType {
  CLICK = 'CLICK',
  KEYDOWN = 'KEYDOWN',
  SCROLL = 'SCROLL',
  PAGE_OPEN = 'PAGE:OPEN',
  PAGE_CLOSE = 'PAGE:CLOSED',
  VIDEO_PLAY = 'VIDEO:PLAY',
  VIDEO_PAUSE = 'VIDEO:PAUSE',
  VIDEO_SEEK = 'VIDEO:SEEK',
  VIDEO_ENDED = 'VIDEO:ENDED',
  VIDEO_VOLUME = 'VIDEO:VOLUME',
  PAGE_SCRAPING = 'PAGE:SCRAPING',
  NO_ACTIVITY = 'NO_ACTIVITY',
}

export class BaseEvent {
  id: string;
  type: EventType;
  domain: string;
  url: string;
  sessionId: string;
  tabId: number;
  created_at: number;
  clientTimezone?: string;
  favIconUrl?: string;
  browserBrand?: string;
  browserVersion?: string;
  platform?: string;
  productId?: string;
  previousEvent?: BaseEvent;
}

export class ClickEvent extends BaseEvent {
  type: EventType.CLICK;
  posX: number;
  posY: number;
}

export class KeydownEvent extends BaseEvent {
  type: EventType.KEYDOWN;
  key: string;
}

export class ScrollEvent extends BaseEvent {
  type: EventType.SCROLL;
  scrollPercentage: number;
  docHeight: number;
  scrollTop: number;
  winHeight: number;
}

export class PageOpenEvent extends BaseEvent {
  type: EventType.PAGE_OPEN;
}

export class PageCloseEvent extends BaseEvent {
  type: EventType.PAGE_CLOSE;
}

export class VideoPlayEvent extends BaseEvent {
  type: EventType.VIDEO_PLAY;
  videoTime: number;
  videoDuration: number;
}

export class VideoPauseEvent extends BaseEvent {
  type: EventType.VIDEO_PAUSE;
  videoTime: number;
  videoDuration: number;
}

export class VideoSeekEvent extends BaseEvent {
  type: EventType.VIDEO_SEEK;
  videoTimeTo: number;
  videoTimeFrom: number;
  videoDuration: number;
}

export class VideoEndedEvent extends BaseEvent {
  type: EventType.VIDEO_ENDED;
  videoDuration: number;
}

export class VideoVolumeEvent extends BaseEvent {
  type: EventType.VIDEO_VOLUME;
  videoVolume: number;
}

export class PageScrapingEvent extends BaseEvent {
  type: EventType.PAGE_SCRAPING;
  contents: Array<object>;
  metadata: Array<object>;
}

export class NoActivityEvent extends BaseEvent {
  type: EventType.NO_ACTIVITY;
}

export type IngestionEvent =
  | ClickEvent
  | KeydownEvent
  | ScrollEvent
  | PageOpenEvent
  | PageCloseEvent
  | VideoPlayEvent
  | VideoPauseEvent
  | VideoSeekEvent
  | VideoEndedEvent
  | VideoVolumeEvent
  | PageScrapingEvent
  | NoActivityEvent;
