import { UserGroup } from '@goteacher/app/analytics/types';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { DomainMetadata } from '@goteacher/app/models/mongo/domain.metadata.model';
import { Product } from '@goteacher/app/models/mongo/product.model';
import { SDPCAgreement } from '@goteacher/app/models/mongo/sdpc.model';
import { Grade } from '@goteacher/app/models/sequelize';
import { Organisation } from '@goteacher/app/models/sequelize/organisation.model';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import { Injectable } from '@nestjs/common';
import { InjectModel as InjectModelMongoose } from '@nestjs/mongoose';
import { InjectModel } from '@nestjs/sequelize';
import mongoose, { Model } from 'mongoose';
import sequelize, { Op, QueryTypes } from 'sequelize';

export enum EnrichmentColumn {
  USER = 'userId',
  SCHOOL = 'schoolId',
  ORGANISATION = 'orgId',
  GRADE = 'grade',
  PRODUCT = 'productId',
  DOMAIN_METADATA = 'domain',
  AGREEMENT = 'agreement',
}

export type CountFilters = {
  schoolIds?: string[];
  grades?: string[];
  role?: UserGroup;
};

export enum AgreementStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  EXPIRED = 'expired',
}

type EnrichmentBase = {
  userId?: string;
  schoolId?: string;
  grade?: string;
  orgId?: string;
  productId?: string;
  domain?: string;
};

type UserEnrichment = {
  user_details: {
    id: string;
    email: string;
    role: string;
    firstName: string;
    lastName: string;
    picture: string;
    schoolId: string;
    schoolName: string;
    grade: string;
  } | null;
};

type SchoolEnrichment = {
  school_details: {
    id: string;
    name: string;
    displayName: string;
  } | null;
  school_user_count?: number;
};

type OrganisationEnrichment = {
  organisation_details: {
    id: string;
    name: string;
    displayName: string;
  } | null;
};

type GradeEnrichment = {
  grade_details: {
    id: string;
    name: string;
    value: string;
  } | null;
  grade_user_count?: number;
};

type ProductEnrichment = {
  productName: string;
  fullDomain?: string;
  product_details: Product | null;
};

type DomainMetadataEnrichment = {
  domain_metadata: DomainMetadata | null;
};

type EnrichmentResult = EnrichmentBase &
  Partial<UserEnrichment> &
  Partial<SchoolEnrichment> &
  Partial<OrganisationEnrichment> &
  Partial<GradeEnrichment> &
  Partial<ProductEnrichment> &
  Partial<DomainMetadataEnrichment> &
  Partial<{
    agreements: SDPCAgreement[];
  }>;

@Injectable()
export class EnrichmentService {
  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
    @InjectModel(Organisation) private organisationModel: typeof Organisation,
    @InjectModel(Grade) private gradeModel: typeof Grade,
    @InjectModelMongoose(Product.name)
    private readonly productModel: Model<Product>,
    @InjectModel(UserSchool) private userSchoolModel: typeof UserSchool,

    @InjectModelMongoose(DomainMetadata.name) private readonly domainMetadataModel: Model<DomainMetadata>,
    @InjectModelMongoose(CustomMetadata.name) private readonly customMetadataModel: Model<CustomMetadata>,
    @InjectModelMongoose(SDPCAgreement.name) private readonly sdpcAgreementModel: Model<SDPCAgreement>,
  ) { }

  async enrich(
    data: EnrichmentBase[],
    enrich: EnrichmentColumn[],
    count?: boolean | string,
    userOrganizationIds?: string[],
    schoolYears?: string[],
    filters?: CountFilters,
    districtId?: string,
  ): Promise<EnrichmentResult[]> {
    const enrichPromises = [];

    if (enrich.includes(EnrichmentColumn.USER)) {
      enrichPromises.push(this.enrichUsers(data));
    }

    if (enrich.includes(EnrichmentColumn.SCHOOL)) {
      enrichPromises.push(this.enrichSchools(data));
      if (count) {
        enrichPromises.push(
          this.countUsersPerSchool(
            data,
            schoolYears,
            filters?.role,
            filters?.grades,
          ),
        );
      }
    }

    if (enrich.includes(EnrichmentColumn.ORGANISATION)) {
      enrichPromises.push(this.enrichOrganisations(data));
    }

    if (enrich.includes(EnrichmentColumn.GRADE)) {
      enrichPromises.push(this.enrichGrades(data));
      if (count && count !== 'raw') {
        enrichPromises.push(
          this.countUsersPerGrade(
            data,
            userOrganizationIds,
            schoolYears,
            filters?.role,
            filters?.schoolIds,
          ),
        );
      } else {
        enrichPromises.push(
          this.rawCountUsersPerGrade(
            data,
            userOrganizationIds,
            schoolYears,
            filters?.role,
          ),
        );
      }
    }

    if (enrich.includes(EnrichmentColumn.PRODUCT)) {
      enrichPromises.push(this.enrichProducts(data));
    }

    if (enrich.includes(EnrichmentColumn.DOMAIN_METADATA)) {
      enrichPromises.push(this.enrichDomainMetadata(data, userOrganizationIds[0]));
    }

    if (enrich.includes(EnrichmentColumn.AGREEMENT)) {
      enrichPromises.push(this.enrichDomainAggreement(data, districtId));
    }

    const results = await Promise.all(enrichPromises);

    let enrichedData = data;
    results.forEach((result) => {
      enrichedData = enrichedData.map((d, index) => ({
        ...d,
        ...result[index],
      }));
    });

    return enrichedData;
  }

  private async enrichUsers(data: any[]): Promise<UserEnrichment[]> {
    const users = await this.userModel.findAll({
      where: {
        id: {
          [Op.in]: data.map((d) => d.userId),
        },
      },
      attributes: ['id', 'email', 'firstName', 'lastName', 'picture', 'role'],
      include: [
        {
          model: UserSchool,
          attributes: ['schoolId', 'grade'],
          include: [
            {
              model: School,
              attributes: ['name'],
            },
          ],
        },
      ],
    });

    return data.map((d) => {
      const user = users.find((u) => u.id === d.userId);
      return {
        user_details: user
          ? {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            picture: user.picture,
            role: user.role,
            schoolId: user.UserSchool?.[0]?.schoolId,
            schoolName: user.UserSchool?.[0]?.school?.name,
            grade: user.UserSchool?.[0]?.grade,
          }
          : null,
      };
    });
  }

  private async enrichSchools(data: any[]): Promise<SchoolEnrichment[]> {
    const schools = await this.schoolModel.findAll({
      where: {
        id: {
          [Op.in]: data.map((d) => d.schoolId),
        },
      },
      attributes: ['id', 'name', 'displayName'],
    });

    return data.map((d) => {
      const school = schools.find((s) => s.id === d.schoolId);
      return {
        school_details: school
          ? {
            id: school.id,
            name: school.name,
            displayName: school.displayName,
          }
          : null,
      };
    });
  }

  private async countUsersPerSchool(
    data: any[],
    schoolYears?: string[],
    role?: UserGroup,
    grades?: string[],
  ) {
    const userSchools = await this.userSchoolModel.findAll({
      where: {
        schoolId: {
          [Op.in]: data.map((d) => d.schoolId),
        },
        ...(schoolYears && {
          schoolYear: {
            [Op.in]: schoolYears,
          },
        }),
        ...(grades && {
          grade: {
            [Op.in]: grades,
          },
        }),
      },
      include: [
        {
          model: User,
          where:
            role === UserGroup.TEACHER
              ? { role: 'teacher' }
              : role === UserGroup.STUDENT
                ? { role: 'student' }
                : undefined,
          attributes: [],
        },
      ],
      attributes: [
        'schoolId',
        [sequelize.fn('COUNT', sequelize.col('userId')), 'userCount'],
      ],
      group: ['schoolId'],
    });

    return data.map((d) => {
      const userCount = userSchools
        .find((us) => us.schoolId === d.schoolId)
        ?.get('userCount');
      return {
        school_user_count: userCount,
      };
    });
  }

  private async enrichOrganisations(
    data: any[],
  ): Promise<OrganisationEnrichment[]> {
    const organisations = await this.organisationModel.findAll({
      where: {
        id: {
          [Op.in]: data.map((d) => d.orgId),
        },
      },
      attributes: ['id', 'name', 'displayName'],
    });

    return data.map((d) => {
      const organisation = organisations.find((o) => o.id === d.orgId);
      return {
        organisation_details: organisation
          ? {
            id: organisation.id,
            name: organisation.name,
            displayName: organisation.displayName,
          }
          : null,
      };
    });
  }

  private async enrichGrades(data: any[]): Promise<GradeEnrichment[]> {
    const grades = await this.gradeModel.findAll({
      where: {
        value: {
          [Op.in]: data.map((d) => d.grade),
        },
      },
      attributes: ['id', 'name', 'value'],
    });

    return data.map((d) => {
      const grade = grades.find((g) => g.value === d.grade);
      return {
        grade_details: grade
          ? {
            id: grade.id,
            name: grade.name,
            value: grade.value,
          }
          : null,
      };
    });
  }

  private async countUsersPerGrade(
    data: any[],
    userOrganizationIds?: string[],
    schoolYears?: string[],
    role?: UserGroup,
    schoolIds?: string[],
  ) {
    const userGrades = await this.userSchoolModel.findAll({
      where: {
        grade: {
          [Op.in]: data.map((d) => d.grade),
        },
        ...(schoolYears && {
          schoolYear: {
            [Op.in]: schoolYears,
          },
        }),
        ...(schoolIds && {
          schoolId: {
            [Op.in]: schoolIds,
          },
        }),
      },
      include: [
        {
          model: School,
          where: userOrganizationIds
            ? {
              organisationId: {
                [Op.in]: userOrganizationIds,
              },
            }
            : undefined,
          attributes: [],
        },
        {
          model: User,
          where:
            role === UserGroup.TEACHER
              ? { role: 'teacher' }
              : role === UserGroup.STUDENT
                ? { role: 'student' }
                : undefined,
          attributes: [],
        },
      ],
      attributes: [
        'grade',
        [sequelize.fn('COUNT', sequelize.col('userId')), 'userCount'],
      ],
      group: ['grade'],
    });

    return data.map((d) => {
      const userCount = userGrades
        .find((ug) => ug.grade === d.grade)
        ?.get('userCount');
      return {
        grade_user_count: userCount,
      };
    });
  }

  private async rawCountUsersPerGrade(
    data: any[],
    userOrganizationIds?: string[],
    schoolYears?: string[],
    role?: UserGroup,
    schoolIds?: string[],
  ) {
    // Base SQL query structure
    let sql = `
      SELECT
        u.role,
        CASE
          WHEN u.role = 'teacher' THEN 'teacher'
          WHEN u.role = 'administrator' THEN 'administrator'
          ELSE us.grade
        END AS final_grade,
        COUNT(us."userId")::integer AS "userCount"
      FROM "userSchool" AS us
      INNER JOIN "users" AS u ON us."userId" = u.id
      INNER JOIN "school" AS s ON us."schoolId" = s.id
      WHERE 1=1
    `;

    const replacements: { [key: string]: any } = {};

    // Add conditions dynamically based on provided filters
    if (schoolYears?.length) {
      sql += ` AND us."schoolYear" IN (:schoolYears)`;
      replacements.schoolYears = schoolYears;
    }
    if (schoolIds?.length) {
      sql += ` AND us."schoolId" IN (:schoolIds)`;
      replacements.schoolIds = schoolIds;
    }
    if (userOrganizationIds?.length) {
      sql += ` AND s."organisationId" IN (:userOrganizationIds)`;
      replacements.userOrganizationIds = userOrganizationIds;
    }

    if (role === UserGroup.TEACHER) {
      sql += ` AND u.role = :role`;
      replacements.role = 'teacher';
    } else if (role === UserGroup.STUDENT) {
      sql += ` AND u.role = :role`;
      replacements.role = 'student';
    }

    sql += `
      GROUP BY final_grade,u.role
    `;

    const userCountsGrouped: { final_grade: string; userCount: number }[] =
      await this.userSchoolModel.sequelize.query(sql, {
        replacements: replacements,
        type: QueryTypes.SELECT,
        raw: true,
      });

    const countsMap: Record<string, number> = {};
    for (const row of userCountsGrouped) {
      countsMap[String(row.final_grade)] = Number(row.userCount) || 0;
    }

    return data.map((d) => {
      const userCount = countsMap[d.grade] || 0;
      return {
        grade_user_count: userCount,
      };
    });
  }

  private async enrichProducts(data: any[]): Promise<ProductEnrichment[]> {
    // Filter out invalid ObjectIds and convert valid ones
    const validProductIds = data
      .filter(
        (d) => d.productId && mongoose.Types.ObjectId.isValid(d.productId),
      )
      .map((d) => new mongoose.Types.ObjectId(d.productId));

    const products = await this.productModel
      .find({
        _id: { $in: validProductIds },
      })
      .select({ name: 1, vendor: 1, type: 1, domainRules: 1 });

    return data.map((d) => {
      const product = products.find((p) => p.id === d.productId);
      return {
        productName: product?.name || 'Other',
        fullDomain: product?.domainRules?.[0]?.pattern,
        product_details: product || null,
      };
    });
  }

  private async enrichDomainMetadata(data: any[], orgId: string): Promise<DomainMetadataEnrichment[]> {
    const domainMetadata = await this.domainMetadataModel.find({
      domain: {
        $in: data.map((d) => d.domain),
      },
    }).lean();

    const customMetadata = await this.customMetadataModel.find({
      domain: {
        $in: data.map((d) => d.domain),
      },
      organizationId: orgId,
    }).lean();

    return data.map((d) => {
      const metadata = domainMetadata.find((dm) => dm.domain === d.domain);
      const custom = customMetadata.find((cm) => cm.domain === d.domain);
      return {
        domain_metadata: { ...metadata, ...custom },
      };
    });
  }

  private async enrichDomainAggreement(
    data: any[],
    districtId: string,
  ): Promise<{
    agreements: SDPCAgreement[];
  }> {
    // Get all agreements for this district
    const allAgreements = await this.sdpcAgreementModel.find({
      districtid: districtId,
    });

    // Helper function to extract domain from website URL
    const extractDomain = (website: string): string => {
      if (!website) return '';
      
      // Remove protocol
      let domain = website.replace(/^https?:\/\//, '');
      // Remove www
      domain = domain.replace(/^www\./, '');
      // Remove path
      domain = domain.split('/')[0];
      // Remove port if any
      domain = domain.split(':')[0];
      
      return domain.toLowerCase();
    };

    // Filter agreements that match any of the domains in data
    const domains = data.map((d) => d.domain?.toLowerCase()).filter(Boolean);
    const agreements = allAgreements.filter((agreement) => {
      const agreementDomain = extractDomain(agreement.website);
      return domains.includes(agreementDomain);
    });

    return {
      agreements,
    };
  }
}
