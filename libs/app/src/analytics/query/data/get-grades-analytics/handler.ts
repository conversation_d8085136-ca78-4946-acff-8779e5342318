import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetGradesAnalyticsQuery } from '@goteacher/app/analytics/query/data/get-grades-analytics/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';

@QueryHandler(GetGradesAnalyticsQuery)
export class GetGradesAnalyticsQueryHandler
  implements IQueryHandler<GetGradesAnalyticsQuery> {
  private readonly logger = new Logger(GetGradesAnalyticsQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetGradesAnalyticsQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const sqlQuery = `
      WITH 
      activity_sessions_cte AS (
        SELECT
          u.userId AS userId,
          u.grade AS grade,
          ${query.productId ? 'as.productId,' : ''}
          ${query.domain ? 'as.domain,' : ''}        
          as.sessionId AS sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(as.startTime) AS day,' : 'toStartOfWeek(as.startTime) AS day,'} 
          sum(as.duration) AS session_duration,
          count(DISTINCT as.url) AS page_views
        FROM goteacher.activity_sessions AS as
        LEFT JOIN goteacher.users u ON u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.productId ? `AND as.productId = '${query.productId}'` : ''}                  
          ${query.domain ? `AND as.domain = '${query.domain}'` : ''}                  
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}        
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          u.userId,
          u.grade,
          ${query.productId ? `as.productId,` : ''}
          ${query.domain ? 'as.domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ),
      
      activity_sessions_summary_cte AS (
        SELECT        
          grade,
          uniqState(userId) AS active_users,
          ${query.productId ? `productId,` : ''}
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) AS sum_duration,
          avg(session_duration) AS avg_duration,
          count(sessionId) AS count_sessions,
          sum(page_views) AS sum_page_views
        FROM activity_sessions_cte
        GROUP BY
          grade
          ${query.productId ? ',productId' : ''}
          ${query.domain ? ',domain' : ''}        
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ),
      
      user_actual_daily_activity_cte AS (
        SELECT 
          u.userId AS userId,
          u.grade AS grade,
          toStartOfDay(as.startTime) AS activity_day,
          sum(as.duration) AS total_duration_on_activity_day
        FROM goteacher.activity_sessions AS as
        LEFT JOIN goteacher.users u ON u.userId = as.userId
        WHERE 
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
        GROUP BY 
          u.userId,
          u.grade,
          activity_day
      ),
      
      quantile_daily_users_cte AS (
        SELECT 
          userId,
          grade,
          quantileExact(0.75)(total_duration_on_activity_day) AS p75_user_daily_duration
        FROM user_actual_daily_activity_cte
        GROUP BY
          userId,
          grade
      ),
      
      quantile_grade_users_cte AS (
        SELECT 
          grade,
          quantileExact(0.75)(p75_user_daily_duration) AS p75_user_daily_duration_by_grade
        FROM quantile_daily_users_cte
        GROUP BY 
          grade
      )
      SELECT 
        ass.grade AS grade,
        ${query.productId ? 'ass.productId,' : ''}
        ${query.domain ? 'ass.domain,' : ''}
              
        sum(ass.count_sessions) AS count_sessions,
        sum(ass.sum_duration) AS sum_time_spent,
        avg(ass.avg_duration) AS avg_session_duration,
      
        sum(ass.sum_page_views) AS page_views,
        uniqMerge(ass.active_users) AS active_users,
        qgu.p75_user_daily_duration_by_grade,

        ${dateDiffInDays > 14
        ? `sum_time_spent / ${dateDiffInDays} * 7 AS avg_screen_time`
        : `sum_time_spent / ${dateDiffInDays} AS avg_screen_time`}
                
      FROM activity_sessions_summary_cte ass
      JOIN quantile_grade_users_cte qgu ON ass.grade = qgu.grade
      GROUP BY 
        ass.grade
        ${query.productId ? ',ass.productId' : ''}
        ${query.domain ? ',ass.domain' : ''}
        ${query.timeGranularity !== TimeGranularity.ALL ? ',ass.day' : ''}
        ,qgu.p75_user_daily_duration_by_grade
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<any>(
        cachingKey,
      );

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    let enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.GRADE,
    ], "raw", userOrganizationIds);

    // adjust screen time
    enrichedData = enrichedData.map((d: any) => {
      if (d.avg_screen_time) {
        d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
      }
      return d;
    });

    await this.cacheService.set(cachingKey, enrichedData, 6 * 60 * 60);

    return enrichedData;
  }
}
