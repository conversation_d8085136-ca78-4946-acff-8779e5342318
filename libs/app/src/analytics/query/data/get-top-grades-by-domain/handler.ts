import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopGradesByDomainQuery } from '@goteacher/app/analytics/query/data/get-top-grades-by-domain/query';
import { TopGradesByDomainResponse } from '@goteacher/app/analytics/query/data/get-top-grades-by-domain/response';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { Grade } from '@goteacher/app/models/sequelize';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { getAcademicYear, PaginationResponse, parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetTopGradesByDomainQuery)
export class GetTopGradesByDomainQueryHandler
  implements IQueryHandler<GetTopGradesByDomainQuery> {
  private readonly logger = new Logger(GetTopGradesByDomainQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(School) private schoolModel: typeof School,
    @InjectModel(Grade) private gradeModel: typeof Grade,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopGradesByDomainQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableGradesIds = await this.getSearchableGradesIds(query);

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'active_users', orderDirection: 'desc' }],
    );

    const sqlQuery = `
    WITH visits_info_cte as (
      ${this.getVisitsSQL(query, userOrganizationIds, searchableGradesIds)}
    ), daily_sessions_cte AS (
      ${this.getDailySessionsSQL(query)}
    )
    SELECT 
      ${query.timeGranularity !== TimeGranularity.ALL ? `sessions.day as day,` : ''}
      sessions.grade as grade,

      sessions.new_active_users as active_users,      
      sessions.count_sessions as count_sessions,
      sessions.sum_time_spent as sum_time_spent,
      sessions.avg_session_duration as avg_session_duration,
      sessions.page_views as page_views
    FROM        
      (
        SELECT 
          ${query.timeGranularity === TimeGranularity.DAILY ? `ds.day as day,` : ''}
          ${query.timeGranularity === TimeGranularity.WEEKLY ? `toStartOfWeek(ds.day) as day,` : ''} 
          ds.grade as grade,
          
          uniqMerge(ds.new_active_users) as new_active_users,          
          
          uniqMerge(ds.count_sessions) as count_sessions,
          sum(ds.sum_time_spent) as sum_time_spent,
          sum(ds.sum_time_spent) / count_sessions as avg_session_duration,

          sum(ds.page_views) as page_views
        FROM daily_sessions_cte ds    
        GROUP BY ds.grade ${query.timeGranularity !== TimeGranularity.ALL ? ', day' : ''}    
    ) sessions    
    ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')}
    LIMIT ${query.limit} 
    OFFSET ${query.offset};
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<
        PaginationResponse<TopGradesByDomainResponse>
      >(cachingKey);

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    // we want to add missing grades with 0 active users based on the query.grades
    const fullGradesData = query.grades ? query.grades.map((grade) => {
      const gradeData = (data as any[]).find((d) => d.grade === grade);
      if (gradeData) return gradeData;
      return {
        grade,
        active_users: 0,
        active_users_student: 0,
        active_users_teacher: 0,
      };
    }) : data;

    const schoolYears = userOrganizationIds.includes(
      'cc5b397a-0e70-4bca-97f5-193ea80a2034',
    )
      ? [getAcademicYear(query.fromDate), getAcademicYear(query.toDate)]
      : undefined;

    const enrichedData = await this.enrichmentService.enrich(
      fullGradesData,
      [EnrichmentColumn.GRADE],
      true,
      userOrganizationIds,
      schoolYears,
      { role: query.userGroup, schoolIds: query.schoolIds },
    );

    const response: PaginationResponse<TopGradesByDomainResponse> = {
      data: enrichedData as TopGradesByDomainResponse[],
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableGradesIds(
    query: GetTopGradesByDomainQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const gradeIds = await this.gradeModel.findAll({
      where: {
        name: {
          [Op.iLike]: `%${query.search}%`,
        },
        value: {
          [Op.iLike]: `%${query.search}%`,
        },
      },
      attributes: ['id', 'value'],
    });

    return gradeIds.map((u) => u.value);
  }

  getVisitsSQL(
    query: GetTopGradesByDomainQuery,
    orgIds: string[],
    searchableGradesIds: string[],
  ): string {
    const orgId = orgIds[0];
    const groupByField = query.productId ? 'productId' : 'domain';
    return `
      SELECT 
        st.sessionId AS sessionId,
        st.tabId AS tabId,
        st.userId AS userId,
        
        --COALESCE(u.orgId, st.orgId) AS orgId,
        --COALESCE(u.schoolId, st.schoolId) AS schoolId,
        --COALESCE(u.role, st.role) AS role,
        --COALESCE(u.grade, st.grade) AS grade,

        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.orgId' : 'COALESCE(u.orgId, st.orgId)'} AS orgId,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.schoolId' : 'COALESCE(u.schoolId, st.schoolId)'} AS schoolId,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.role' : 'COALESCE(u.role, st.role)'} AS role,
        ${orgId === '7d872d49-0a41-40a1-96aa-f980ad5f025e' ? 'st.grade' : 'COALESCE(u.grade, st.grade)'} AS grade,
        
        st.${groupByField} AS ${groupByField},
        st.url AS url,
        min(st.visit_start) AS visit_start,
        max(st.visit_end) AS visit_end,
        toStartOfDay(min(st.visit_start)) AS day,
        toStartOfWeek(min(st.visit_start)) AS week,
        any(st.visit_timezone) AS visit_timezone,
        dateDiff('s', visit_start, visit_end) AS time_spent,
        if(
            (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) >= 7
            )
            AND (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) <= 14
            ),
            1,
            0
        ) AS in_class,

        count(DISTINCT st.url) as page_views
      FROM goteacher.visits_sc as st
      LEFT JOIN goteacher.users u on u.userId = st.userId
      WHERE
        orgId = '${orgId}'
        AND st.${groupByField} = '${query.productId || query.domain}'
        ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
        ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
        ${query.fromDate ? `AND st.visit_start >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
        ${query.toDate ? `AND st.visit_start <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}  
        ${searchableGradesIds.length ? `AND grade IN (${searchableGradesIds.map((sui) => `'${sui}'`).join(',')})` : ''}               
      GROUP BY
          sessionId,
          tabId,
          userId,
          orgId,
          schoolId,
          role,
          grade,
          ${groupByField},
          url      
     `;
  }

  getDailySessionsSQL(query: GetTopGradesByDomainQuery): string {
    const groupByField = query.productId ? 'productId' : 'domain';

    return `
      SELECT         
        schoolId,
        orgId,
        grade,
        ${groupByField},
        ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'} 
        
        uniqState(userId) as new_active_users,        
        
        uniqState(sessionId) as count_sessions,
        sum(time_spent) as sum_time_spent,

        sum(page_views) as page_views        
    FROM visits_info_cte
    GROUP BY
        schoolId,
        orgId,
        grade,        
        ${query.timeGranularity === 'all' ? '' : 'day,'} 
        ${groupByField}
    `;
  }
}
