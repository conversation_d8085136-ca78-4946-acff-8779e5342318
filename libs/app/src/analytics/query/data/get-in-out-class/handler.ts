import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetInOutClassQuery } from '@goteacher/app/analytics/query/data/get-in-out-class/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

export interface InOutClass {
  day?: Date | string;
  total: number;
  in_class: number;
  out_class: number;
}

@QueryHandler(GetInOutClassQuery)
export class GetInOutClassQueryHandler
  implements IQueryHandler<GetInOutClassQuery> {
  private readonly logger = new Logger(GetInOutClassQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetInOutClassQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT            
          ${query.productId ? `as.productId AS productId,` : 'as.domain AS domain,'}
          as.sessionId as sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(as.startTime) AS day,' : 'toStartOfWeek(as.startTime) AS day,'} 
          sum(as.duration) as session_duration,          
          sumIf(as.duration, toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) >= 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14) AS session_duration_in_class,          
          sumIf(as.duration, NOT (toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) >= 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14)) AS session_duration_out_class    
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})            
          ${query.productId ? `AND as.productId = '${query.productId}'` : `AND as.domain = '${query.domain}'`}                  
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          ${query.productId ? `as.productId,` : 'as.domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ), activity_sessions_summary_cte AS (
        SELECT            
          ${query.productId ? `productId,` : 'domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) as sum_duration,
          avg(session_duration) as avg_duration,
          count(sessionId) as count_sessions,
          sum(session_duration_in_class) AS sum_duration_in_class,
          sum(session_duration_out_class) AS sum_duration_out_class
        FROM activity_sessions_cte
        GROUP BY            
          ${query.productId ? `productId` : 'domain'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'} 
        
        sum(ass.sum_duration) as total,
        sum(ass.sum_duration_in_class) as in_class,
        sum(ass.sum_duration_out_class) as out_class
      FROM 
        activity_sessions_summary_cte ass
      ${query.timeGranularity === TimeGranularity.ALL ? '' : ' GROUP BY day'}
      ${query.timeGranularity !== TimeGranularity.ALL ? `ORDER BY day ASCENDING` : ''}
      ;
    `;

    this.logger.debug(`sqlQuery in-out-class: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: InOutClass[];
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data: data };
  }
}
