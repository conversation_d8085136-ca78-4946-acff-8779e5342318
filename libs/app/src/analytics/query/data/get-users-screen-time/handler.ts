import { IQ<PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { TimeGranularity } from '@goteacher/app/analytics';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { GetUserScreenTime } from './query';

@QueryHandler(GetUserScreenTime)
export class GetUserScreenTimeHandler
  implements IQueryHandler<GetUserScreenTime> {
  private readonly logger = new Logger(GetUserScreenTimeHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserScreenTime): Promise<any> {
    const userOrganizationId = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    )[0];

    if (query.timeGranularity === TimeGranularity.ALL) {
      query.timeGranularity = TimeGranularity.WEEKLY;
    }

    const sqlQuery = `
      SELECT
        userId,
        ${query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(startTime) AS day,' : 'toStartOfWeek(startTime) AS day,'}          
        sum(duration) as sum_time_spent
      FROM goteacher.activity_sessions as act
      LEFT JOIN goteacher.users u on u.userId = act.userId
      WHERE
        u.orgId = '${userOrganizationId}'
        AND u.userId = '${query.userId}'
        ${query.fromDate ? `AND toDateTime(day) >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
        ${query.toDate ? `AND toDateTime(day) <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
        ${userOrganizationId === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 15` : ''}
      GROUP BY userId, day
      ORDER BY day
    `;

    this.logger.debug(`sqlQuery ${query.timeGranularity}: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data };
  }
}
