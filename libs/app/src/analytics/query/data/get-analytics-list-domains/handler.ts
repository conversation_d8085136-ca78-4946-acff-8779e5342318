import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopDomainsQuery } from '@goteacher/app/analytics/query/data/get-analytics-list-domains/query';
import {
  AgreementStatus,
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service/enrichment.service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import {
  Contract,
  OrganizationDomainMetadata,
  PaymentStatus,
  SDPCAgreement,
} from '@goteacher/app/models/mongo';
import { CustomMetadata } from '@goteacher/app/models/mongo/custom.metadata.model';
import { DomainMetadata } from '@goteacher/app/models/mongo/domain.metadata.model';
import { parseOrderBy } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@QueryHandler(GetTopDomainsQuery)
export class GetTopDomainsQueryHandler
  implements IQueryHandler<GetTopDomainsQuery> {
  private readonly logger = new Logger(GetTopDomainsQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    @InjectModel(Contract.name) private readonly contractModel: Model<Contract>,
    @InjectModel(OrganizationDomainMetadata.name)
    private readonly metadataModel: Model<OrganizationDomainMetadata>,
    @InjectModel(DomainMetadata.name) private readonly domainMetadataModel: Model<DomainMetadata>,
    @InjectModel(CustomMetadata.name) private readonly customMetadataModel: Model<CustomMetadata>,
    @InjectModel(SDPCAgreement.name) private readonly sdpcAgreementModel: Model<SDPCAgreement>,
    private readonly enrichmentService: EnrichmentService,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopDomainsQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    //test districtId DEV: '9139'

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    let filterDomainClause = '';
    if (query.price) {
      const contractsDomains = await this.contractModel
        .find({
          organizationId: {
            $in: userOrganizationIds,
          },
          // active: true,
          deleted: false,
          // subscriptionStartDate: { $lte: new Date() },
          // subscriptionEndDate: { $gte: new Date() },
        })
        .select('domain');

      if (query.price === PaymentStatus.PAID) {
        if (contractsDomains.length === 0) {
          return {
            data: [],
            total: 0,
            limit: query.limit,
            offset: query.offset,
          };
        }
        filterDomainClause = ` AND domain IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
      } else if (query.price === PaymentStatus.FREE) {
        if (contractsDomains.length > 0) {
          filterDomainClause = ` AND domain NOT IN (${contractsDomains.map((c) => `'${c.domain}'`).join(',')})`;
        }
      }
    }

    if (query.approvalStatus) {
      const metadataDomains = await this.metadataModel
        .find({
          organizationId: {
            $in: userOrganizationIds,
          },
          approvalStatus: query.approvalStatus,
        })
        .select('domain');
      const inDomains = metadataDomains.map((d) => d.domain);
      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
          limit: query.limit,
          offset: query.offset,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }

    if (query.riskLevel) {
      const customMetadataDomains = await this.customMetadataModel
        .find({
          riskLevel: query.riskLevel,
        })
        .select('domain');
      let inDomains = customMetadataDomains.map((d) => d.domain);

      const metadataDomains = await this.domainMetadataModel
        .find({
          riskLevel: query.riskLevel,
          domain: { $nin: inDomains },
        })
        .select('domain');

      inDomains = [...inDomains, ...metadataDomains.map((d) => d.domain)];

      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }

    if (query.categories) {
      const customMetadataDomains = await this.customMetadataModel
        .find({
          categories: { $in: query.categories },
        })
        .select('domain');
      let inDomains = customMetadataDomains.map((d) => d.domain);

      const metadataDomains = await this.domainMetadataModel
        .find({
          categories: { $in: query.categories },
          domain: { $nin: inDomains },
        })
        .select('domain');

      inDomains = [...inDomains, ...metadataDomains.map((d) => d.domain)];

      if (inDomains.length === 0) {
        return {
          data: [],
          total: 0,
        };
      }
      filterDomainClause += ` AND domain IN (${inDomains.map((d) => `'${d}'`).join(',')})`;
    }


    // Helper function to extract domain from website URL (shared between filter and status calculation)
    const extractDomainForMatching = (website: string): string => {
      if (!website) return '';
      
      // Remove protocol
      let domain = website.replace(/^https?:\/\//, '');
      // Remove www
      domain = domain.replace(/^www\./, '');
      // Remove path
      domain = domain.split('/')[0];
      // Remove port if any
      domain = domain.split(':')[0];
      
      return domain.toLowerCase();
    };

    this.logger.debug(`SDPC Filter Check - sdpcAgreementStatus: ${query.sdpcAgreementStatus}, sdpcDistrictId: ${query.ctx.sdpcDistrictId}`);
    
    if (query.sdpcAgreementStatus && query.ctx.sdpcDistrictId) {
      this.logger.debug(`SDPC Filter - Status: ${query.sdpcAgreementStatus}, District: ${query.ctx.sdpcDistrictId}`);
      
      // Get all SDPC agreements for this district
      const allSdpcAgreements = await this.sdpcAgreementModel.find({
        districtid: query.ctx.sdpcDistrictId,
      }).select('website date_expired date_approved');
      
      this.logger.debug(`SDPC Filter - Found ${allSdpcAgreements.length} agreements for district`);

      let filterDomains: string[] = [];

      if (query.sdpcAgreementStatus === AgreementStatus.ACTIVE) {
        // Active: has agreement with valid dates
        const today = new Date();
        this.logger.debug(`SDPC Filter - Checking for ACTIVE agreements. Today: ${today.toISOString()}`);
        
        filterDomains = allSdpcAgreements
          .filter((agreement) => {
            const dateApproved = agreement.date_approved ? new Date(agreement.date_approved) : null;
            const dateExpired = agreement.date_expired ? new Date(agreement.date_expired) : null;
            
            this.logger.debug(`SDPC Filter - Agreement: ${agreement.website}, approved: ${dateApproved?.toISOString()}, expired: ${dateExpired?.toISOString()}`);
            
            if (dateApproved && dateExpired) {
              return today >= dateApproved && today <= dateExpired;
            } else if (dateApproved && !dateExpired) {
              // No expiry date means still active
              return today >= dateApproved;
            }
            return false;
          })
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Active domains found: ${JSON.stringify(filterDomains)}`);
      } else if (query.sdpcAgreementStatus === AgreementStatus.EXPIRED) {
        // Expired: has expired agreement
        const today = new Date();
        this.logger.debug(`SDPC Filter - Checking for EXPIRED agreements`);
        
        filterDomains = allSdpcAgreements
          .filter((agreement) => {
            const dateExpired = agreement.date_expired ? new Date(agreement.date_expired) : null;
            const isExpired = dateExpired && today > dateExpired;
            this.logger.debug(`SDPC Filter - Agreement: ${agreement.website}, expired: ${dateExpired?.toISOString()}, isExpired: ${isExpired}`);
            return isExpired;
          })
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Expired domains found: ${JSON.stringify(filterDomains)}`);
      } else if (query.sdpcAgreementStatus === AgreementStatus.INACTIVE) {
        // Inactive: no agreement at all
        this.logger.debug(`SDPC Filter - Checking for INACTIVE (no agreement) domains`);
        
        const agreementDomains = allSdpcAgreements
          .map(agreement => extractDomainForMatching(agreement.website))
          .filter(Boolean);
        
        this.logger.debug(`SDPC Filter - Domains with agreements: ${JSON.stringify(agreementDomains)}`);
        
        // We need to exclude domains that have agreements
        if (agreementDomains.length > 0) {
          filterDomainClause += ` AND domain NOT IN (${agreementDomains.map((d) => `'${d}'`).join(',')})`;
          this.logger.debug(`SDPC Filter - Added NOT IN clause for INACTIVE filter`);
        } else {
          this.logger.debug(`SDPC Filter - No agreements found, all domains are INACTIVE`);
        }
        // If no agreements exist, all domains are inactive - no additional filter needed
      }

      // Apply filter for ACTIVE and INACTIVE_W_CONTRACT statuses
      if (query.sdpcAgreementStatus !== AgreementStatus.INACTIVE) {
        this.logger.debug(`SDPC Filter - Filter domains for ${query.sdpcAgreementStatus}: ${JSON.stringify(filterDomains)}`);
        
        if (filterDomains.length === 0) {
          this.logger.debug(`SDPC Filter - No domains match criteria, returning empty result`);
          // No domains match the criteria
          return {
            data: [],
            total: 0,
            limit: query.limit,
            offset: query.offset,
          };
        }
        filterDomainClause += ` AND domain IN (${filterDomains.map((d) => `'${d}'`).join(',')})`;
      }
      
      this.logger.debug(`SDPC Filter - Final filterDomainClause: ${filterDomainClause}`);
    }

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const calculatedRange = dateDiffInDays > 14 ? dateDiffInDays / 7 : dateDiffInDays;
    let avgTimeSpentCalculation = `sum(dscte.sum_time_spent) / ${calculatedRange} as avg_screen_time,`;
    if (query.userGroup === UserGroup.TEACHER) {
      avgTimeSpentCalculation = `sum(dscte.sum_time_spent_teacher) / ${calculatedRange} as avg_screen_time,`;
    }
    if (query.userGroup === UserGroup.STUDENT) {
      avgTimeSpentCalculation = `sum(dscte.sum_time_spent_student) / ${calculatedRange} as avg_screen_time,`;
    }

    this.logger.debug(`SDPC Filter - Final filterDomainClause before SQL: ${filterDomainClause}`);
    
    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT 
          domain,
          userId,
          sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(as.startTime) AS day,' : 'toStartOfWeek(as.startTime) AS day,'} 
          sum(duration) as session_duration,
          count(DISTINCT as.url) as page_views
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          AND domain != 'newtab.'
          ${filterDomainClause}
          ${query.search ? `AND domain ILIKE '%${query.search}%'` : ''}
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          domain,
          userId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ), activity_sessions_summary_cte AS (
        SELECT 
          domain,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) as sum_duration,
          avg(session_duration) as avg_duration,
          count(sessionId) as count_sessions,
          sum(page_views) as sum_page_views,
          uniqState(userId) as active_users
        FROM activity_sessions_cte
        GROUP BY
          domain
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ${query.timeGranularity === 'all' ? '' : 'day,'}
        domain,             
                  
        sum(ass.count_sessions) as count_sessions,        
        sum(ass.sum_duration) as sum_time_spent,        
        avg(ass.avg_duration) as avg_session_duration,        
        avg(ass.avg_duration) as avg_time_spent,        
        sum(ass.sum_page_views) as page_views,              
        uniqMerge(ass.active_users) as active_users,           
        sum_time_spent / ${dateDiffInDays} as avg_screen_time
      FROM activity_sessions_summary_cte ass
      GROUP BY
        domain
        ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};                    
    `;
    this.logger.debug(`sqlQuery get-analytics-list-domains: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
      total: number;
      limit: number;
      offset: number;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    let enrichedData = await this.enrichmentService.enrich(
      data,
      [EnrichmentColumn.DOMAIN_METADATA, EnrichmentColumn.AGREEMENT],
      false,
      userOrganizationIds,
      undefined,
      undefined,
      query.ctx.sdpcDistrictId,
    );

    const agreements = enrichedData.map((d) => d.agreements).flat() || [];

    // Get all SDPC agreements for this district for status calculation
    const allSdpcAgreementsForStatus = query.ctx.sdpcDistrictId ? await this.sdpcAgreementModel.find({
      districtid: query.ctx.sdpcDistrictId,
    }).select('website date_expired date_approved') : [];

    const agreement_status = enrichedData.map((d) => {
      // Use the same data source as filtering logic
      const agreement = allSdpcAgreementsForStatus.find((dm) => {
        if (!dm?.website || !d.domain) return false;
        return extractDomainForMatching(dm.website) === d.domain.toLowerCase();
      });
      if (agreement) {
        const today = new Date();
        const dateApproved = agreement.date_approved
          ? new Date(agreement.date_approved)
          : null;
        const dateExpired = agreement.date_expired
          ? new Date(agreement.date_expired)
          : null;

        if (dateApproved && dateExpired) {
          if (today >= dateApproved && today <= dateExpired) {
            return AgreementStatus.ACTIVE;
          } else {
            return AgreementStatus.EXPIRED;
          }
        } else if (dateApproved && !dateExpired) {
          return AgreementStatus.ACTIVE;
        } else {
          return AgreementStatus.INACTIVE;
        }
      }
      return AgreementStatus.INACTIVE;
    });

    enrichedData = enrichedData.map((d, index) => {
      const agreement = agreements.find((dm) => {
        if (!dm?.website || !d.domain) return false;
        return extractDomainForMatching(dm.website) === d.domain.toLowerCase();
      });
      return {
        ...d,
        agreement_details: agreement || null,
        agreement_status: agreement_status[index],
      };
    });

    // adjust screen time
    enrichedData = enrichedData.map((d: any) => {
      if (d.avg_screen_time) {
        if (query.userGroup === UserGroup.BOTH) {
          d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
        } else if (query.userGroup === UserGroup.TEACHER) {
          d.avg_screen_time = +d.active_users_teacher > 0 ? d.avg_screen_time / d.active_users_teacher : 0;
        } else if (query.userGroup === UserGroup.STUDENT) {
          d.avg_screen_time = +d.active_users_student > 0 ? d.avg_screen_time / d.active_users_student : 0;
        }
      }
      return d;
    });

    await this.cacheService.set(
      cachingKey,
      {
        data: enrichedData,
        total: rows_before_limit_at_least,
        limit: query.limit,
        offset: query.offset,
      },
      6 * 60 * 60,
    );

    return {
      data: enrichedData,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
  }
}
