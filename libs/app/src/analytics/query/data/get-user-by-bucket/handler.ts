import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetUserByBucketQuery } from '@goteacher/app/analytics/query/data/get-user-by-bucket/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { UserGroup } from '@goteacher/app/analytics/types';

export interface UserByBucket {
  orgId: string;
  domain: string;
  uniq_user_spent_20: number;
  uniq_user_spent_40: number;
  uniq_user_spent_60: number;
  uniq_user_spent_120: number;
  uniq_user_spent_120_plus: number;
  no_login: number;
}

@QueryHandler(GetUserByBucketQuery)
export class GetUserByBucketQueryHandler
  implements IQueryHandler<GetUserByBucketQuery> {
  private readonly logger = new Logger(GetUserByBucketQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserByBucketQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const sqlQuery = `
      WITH avg_weekly as (
        WITH activity_sessions_cte AS (
          SELECT            
            u.userId as userId,
            ${query.productIds?.length ? `as.productId AS productId,` : 'as.domain AS domain,'}
            as.sessionId as sessionId,
            toStartOfWeek(as.startTime) AS week,
            sum(as.duration) as session_duration          
          FROM goteacher.activity_sessions as as
          LEFT JOIN goteacher.users u on u.userId = as.userId
          WHERE
            u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
            ${query.productIds?.length ? `AND as.productId IN (${query.productIds.map((productId) => `'${productId}'`).join(',')})` : `AND as.domain IN (${query.domains.map((domain) => `'${domain}'`).join(',')})`}                  
            ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
            ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
            ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
            ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
            ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
            ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 15` : ''}
          GROUP BY            
            u.userId,
            ${query.productIds?.length ? `productId,` : 'domain,'}
            week,
            sessionId
        ), activity_sessions_summary_cte AS (
          SELECT            
            userId,
            ${query.productIds?.length ? `productId,` : 'domain,'}            
            sum(session_duration) as sum_duration,
            avg(session_duration) as avg_duration,
            count(sessionId) as count_sessions
          FROM activity_sessions_cte
          GROUP BY            
            userId,
            ${query.productIds?.length ? `productId,` : 'domain,'}
            week
        )
        SELECT 
          ass.userId as userId,                     
          ass.${query.productIds?.length ? 'productId' : 'domain'} as ${query.productIds?.length ? 'productId' : 'domain'}, 
          avg(ass.sum_duration) as avg_weekly_sum_duration
        FROM activity_sessions_summary_cte ass
        GROUP BY
          userId, 
          ${query.productIds?.length ? 'productId' : 'domain'}
      )
      SELECT 
        --ds.orgId as orgId,
        ds.${query.productIds?.length ? 'productId' : 'domain'} as ${query.productIds?.length ? 'productId,' : 'domain,'}
        uniqIf(
          ds.userId, 
          ds.avg_weekly_sum_duration <= 20 * 60) as uniq_user_spent_20,
        uniqIf(
          ds.userId,
          ds.avg_weekly_sum_duration <= 40 * 60
          AND ds.avg_weekly_sum_duration > 20 * 60
        ) as uniq_user_spent_40,
        uniqIf(
          ds.userId,
          ds.avg_weekly_sum_duration <= 60 * 60
          AND ds.avg_weekly_sum_duration > 40 * 60
        ) as uniq_user_spent_60,
        uniqIf(
          ds.userId,
          ds.avg_weekly_sum_duration <= 120 * 60
          AND ds.avg_weekly_sum_duration > 60 * 60
        ) as uniq_user_spent_120,
        uniqIf(
          ds.userId,
          ds.avg_weekly_sum_duration > 120 * 60
        ) as uniq_user_spent_120_plus        
      FROM 
        avg_weekly ds      
      GROUP BY 
      --orgId,
      ${query.productIds?.length ? 'productId' : 'domain'}        
      ;
    `;
    this.logger.debug(`sqlQuery user-by-bucket: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{ data: UserByBucket[] }>(
      cachingKey,
    );
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json<UserByBucket[]>();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);
    return { data: data };
  }
}
