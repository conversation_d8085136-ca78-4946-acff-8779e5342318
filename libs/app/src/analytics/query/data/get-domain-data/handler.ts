import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetDomainDataQuery } from '@goteacher/app/analytics/query/data/get-domain-data/query';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { isMoreThanTwoWeeks } from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

@QueryHandler(GetDomainDataQuery)
export class GetDomainDataQueryHandler
  implements IQueryHandler<GetDomainDataQuery> {
  private readonly logger = new Logger(GetDomainDataQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetDomainDataQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const tableToBeUsed = query.productIds?.length
      ? shouldAggregateAll
        ? 'all_product_metrics_sc'
        : query.timeGranularity === TimeGranularity.DAILY
          ? 'daily_product_metrics_sc'
          : shouldUseWeekly
            ? 'weekly_product_metrics_sc'
            : 'daily_product_metrics_sc'
      : shouldAggregateAll
        ? 'all_domain_metrics_sc'
        : query.timeGranularity === TimeGranularity.DAILY
          ? 'daily_domain_metrics_sc'
          : shouldUseWeekly
            ? 'weekly_domain_metrics_sc'
            : 'daily_domain_metrics_sc';

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    const sqlQuery = `
      WITH activity_sessions_cte AS (
        SELECT 
          ${query.productIds?.length ? 'productId,' : 'domain,'}
          ${query.productIds?.length ? 'fullDomain,' : ''}
          sessionId,
          userId,
          u.role as role,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(startTime) AS day,' : 'toStartOfWeek(startTime) AS day,'} 
          sum(duration) as session_duration,          
          count(DISTINCT as.url) as page_views
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.productIds?.length ? `AND as.productId IN (${query.productIds.map((p) => `'${p}'`).join(',')})` : `AND as.domain IN (${query.domains.map((d) => `'${d}'`).join(',')})`}                  
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
        GROUP BY
          ${query.productIds?.length ? 'productId,' : 'domain,'}
          ${query.productIds?.length ? 'fullDomain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId,
          userId,
          role
      ), activity_sessions_summary_cte AS (
        SELECT 
          ${query.productIds?.length ? 'productId,' : 'domain,'}
          ${query.productIds?.length ? 'fullDomain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}

          sum(session_duration) as sum_duration,                    
          avg(session_duration) as avg_duration,          
          count(sessionId) as count_sessions,          
          uniqState(userId) as active_users,          
          sum(page_views) as sum_page_views          
        FROM activity_sessions_cte
        GROUP BY
          ${query.productIds?.length ? 'productId' : 'domain'}
          ${query.productIds?.length ? ',fullDomain' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'ass.day as day,'}  
        ${query.productIds?.length ? 'ass.productId as productId,' : 'ass.domain as domain,'}
        ${query.productIds?.length ? 'ass.fullDomain as fullDomain,' : ''}        

        sum(ass.sum_duration) as sum_time_spent,        
        avg(ass.avg_duration) as avg_session_duration,        
        avg(ass.avg_duration) as avg_time_spent,        
        sum(ass.count_sessions) as count_sessions,        
        sum(ass.sum_page_views) as page_views,        
        uniqMerge(ass.active_users) as active_users,        
        sum_time_spent / ${dateDiffInDays} as avg_screen_time      
      FROM activity_sessions_summary_cte as ass
      GROUP BY
        ${query.productIds?.length ? 'productId' : 'domain'}        
        ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      ${query.timeGranularity === TimeGranularity.ALL ? '' : 'ORDER BY day ASC'}              
    `;
    this.logger.debug(`sqlQuery get-domain-data: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    let { data } = await queryResult.json();


    // adjust screen time
    data = data.map((d: any) => {
      if (d.avg_screen_time) {
        if (query.userGroup === UserGroup.BOTH) {
          d.avg_screen_time = +d.active_users > 0 ? d.avg_screen_time / d.active_users : 0;
        } else if (query.userGroup === UserGroup.TEACHER) {
          d.avg_screen_time = +d.active_users_teacher > 0 ? d.avg_screen_time / d.active_users_teacher : 0;
        } else if (query.userGroup === UserGroup.STUDENT) {
          d.avg_screen_time = +d.active_users_student > 0 ? d.avg_screen_time / d.active_users_student : 0;
        }
      }
      return d;
    });

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data };
  }
}
