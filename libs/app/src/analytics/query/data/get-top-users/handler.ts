import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetTopUsersQuery } from '@goteacher/app/analytics/query/data/get-top-users/query';
import { TopUsersResponse } from '@goteacher/app/analytics/query/data/get-top-users/response';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import {
  isMoreThanTwoWeeks,
  PaginationResponse,
  parseOrderBy,
} from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQ<PERSON>y<PERSON>and<PERSON>, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetTopUsersQuery)
export class GetTopUsersQueryHandler
  implements IQueryHandler<GetTopUsersQuery> {
  private readonly logger = new Logger(GetTopUsersQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetTopUsersQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableUserIds = await this.getSearchableUserIds(
      userOrganizationIds,
      query,
    );

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const sqlQuery = `
      WITH activity_sessions_summary_cte AS (
        SELECT
          ism.userId as userId,
          ${query.productId ? `ism.productId AS productId,` : 'ism.domain AS domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(ism.startTime) AS day,' : 'toStartOfWeek(ism.startTime) AS day,'}
          sum(ism.duration) as sum_duration,
          quantileExact(0.5)(ism.duration) as median_duration,
          count(DISTINCT ism.sessionId) as count_sessions
        FROM goteacher.interaction_summaries_local ism
        WHERE
          ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          ${query.productId ? `AND ism.productId = '${query.productId}'` : `AND ism.domain = '${query.domain}'`}
          ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND ism.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND ism.startTime >= '${query.fromDate.toISOString()}'` : ''}
          ${query.toDate ? `AND ism.startTime <= '${query.toDate.toISOString()}'` : ''}
          ${searchableUserIds.length ? `AND ism.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 14` : ''}
        GROUP BY
          userId,
          ${query.productId ? `productId` : 'domain'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ${query.timeGranularity !== TimeGranularity.ALL ? `metrics.day as day,` : ''}
        ass.userId as userId, 
        metrics.page_views as page_views,
        
        ass.sum_duration as sum_time_spent,
        COALESCE(ass.median_duration, 0) as avg_time_spent,
        ass.count_sessions as count_sessions
      FROM activity_sessions_summary_cte ass     
      LEFT JOIN 
        (
          SELECT 
            ${query.timeGranularity === TimeGranularity.DAILY ? `admps.day as day,` : ''}
            ${query.timeGranularity === TimeGranularity.WEEKLY ? `toStartOfWeek(admps.day) as day,` : ''} 
            admps.userId as userId,
            countMergeOrDefault(page_views) as page_views
          FROM 
            ${shouldAggregateAll ? (query.productId ? 'all_product_metrics_per_user_sc' : 'all_domain_metrics_per_user_sc') : shouldUseWeekly ? (query.productId ? 'weekly_product_metrics_per_user_sc' : 'weekly_domain_metrics_per_user_sc') : query.productId ? 'daily_product_metrics_per_user_sc' : 'daily_domain_metrics_per_user_sc'} admps         
          WHERE 
            ${query.productId ? `admps.productId = '${query.productId}'` : `admps.domain = '${query.domain}'`} 
            AND admps.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
            ${query.schoolIds?.length
        ? `AND admps.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})`
        : ''
      }
            ${query.fromDate ? `AND admps.day >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
            ${query.toDate ? `AND admps.day <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          GROUP BY admps.userId ${query.timeGranularity !== TimeGranularity.ALL ? ', day' : ''}
        ) metrics ON ass.userId = metrics.userId ${query.timeGranularity !== TimeGranularity.ALL ? 'AND ass.day = metrics.day' : ''}    
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<PaginationResponse<TopUsersResponse>>(
        cachingKey,
      );

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER,
    ]);

    const response: PaginationResponse<TopUsersResponse> = {
      data: enrichedData as TopUsersResponse[],
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableUserIds(
    orgIds: string[],
    query: GetTopUsersQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: query.schoolIds?.length
                ? query.schoolIds
                : schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

  getVisitsSQL(
    query: GetTopUsersQuery,
    orgIds: string[],
    searchableUserIds: string[],
    shouldUseWeekly: boolean,
  ): string {
    const orgId = orgIds[0];
    return `
      SELECT 
        st.sessionId AS sessionId,
        st.tabId AS tabId,
        st.userId AS userId,
        COALESCE(u.orgId, st.orgId) AS orgId,
        COALESCE(u.schoolId, st.schoolId) AS schoolId,
        COALESCE(u.role, st.role) AS role,
        COALESCE(u.grade, st.grade) AS grade,
        ${query.productId ? 'st.productId AS productId,' : 'st.domain AS domain,'}
        st.url AS url,
        min(st.visit_start) AS visit_start,
        max(st.visit_end) AS visit_end,
        toStartOfDay(min(st.visit_start)) AS day,
        toStartOfWeek(min(st.visit_start)) AS week,
        any(st.visit_timezone) AS visit_timezone,
        dateDiff('s', visit_start, visit_end) AS time_spent,
        if(
            (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) >= 7
            )
            AND (
                toHour(
                    addHours(visit_start, toInt32OrZero(visit_timezone))
                ) <= 14
            ),
            1,
            0
        ) AS in_class
      FROM goteacher.visits_sc as st
      LEFT JOIN goteacher.users u on u.userId = st.userId
      WHERE 
        orgId = '${orgId}'
        ${query.productId ? `AND st.productId = '${query.productId}'` : `AND st.domain = '${query.domain}'`}            
        ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
        ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
         ${query.fromDate ? (shouldUseWeekly ? `AND toDateTime(toStartOfWeek(st.visit_start)) >= ${Math.round(query.fromDate.getTime() / 1000)}` : `AND toStartOfDay(st.visit_start) >= ${Math.round(query.fromDate.getTime() / 1000)}`) : ''}
        ${query.toDate ? (shouldUseWeekly ? `AND toDateTime(toStartOfWeek(st.visit_start)) <= ${Math.round(query.toDate.getTime() / 1000)}` : `AND toStartOfDay(st.visit_start) <= ${Math.round(query.toDate.getTime() / 1000)}`) : ''}            
        ${searchableUserIds.length ? `AND st.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY
          sessionId,
          tabId,
          userId,
          orgId,
          schoolId,
          role,
          grade,
          ${query.productId ? 'productId,' : 'domain,'}
          url             
     `;
  }

  getDailySessionsSQL(query: GetTopUsersQuery): string {
    return `
      SELECT 
        userId,
        schoolId,
        orgId,
        grade,        
        ${query.productId ? 'productId,' : 'domain,'}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'} 
        quantileExactOrNull(0.5)(time_spent) AS avg_time_spent,
        quantileExactIfOrNull(0.5)(time_spent, role = 'student') AS avg_time_spent_student,
        quantileExactIfOrNull(0.5)(time_spent, role = 'teacher') AS avg_time_spent_teacher,
        quantileExactIfOrNull(0.5)(time_spent, role = 'administrator') AS avg_time_spent_administrator,
        sumOrDefault(time_spent) AS sum_time_spent,
        sumIfOrDefault(time_spent, role = 'student') AS sum_time_spent_student,
        sumIfOrDefault(time_spent, role = 'teacher') AS sum_time_spent_teacher,
        sumIfOrDefault(time_spent, role = 'administrator') AS sum_time_spent_administrator,
        uniqState(sessionId) AS count_sessions,
        uniqStateIf(sessionId, role = 'student') AS count_sessions_student,
        uniqStateIf(sessionId, role = 'teacher') AS count_sessions_teacher,
        uniqStateIf(sessionId, role = 'administrator') AS count_sessions_administrator,
        quantileExactIfOrNull(0.5)(time_spent, in_class) AS avg_time_spent_in_class,
        quantileExactIfOrNull(0.5)(
            time_spent,
            in_class
            AND (role = 'student')
        ) AS avg_time_spent_in_class_student,
        quantileExactIfOrNull(0.5)(
            time_spent,
            in_class
            AND (role = 'teacher')
        ) AS avg_time_spent_in_class_teacher,
        quantileExactIfOrNull(0.5)(
            time_spent,
            in_class
            AND (role = 'administrator')
        ) AS avg_time_spent_in_class_administrator,
        sumIfOrDefault(time_spent, in_class) AS sum_time_spent_in_class,
        sumIfOrDefault(
            time_spent,
            in_class
            AND (role = 'student')
        ) AS sum_time_spent_in_class_student,
        sumIfOrDefault(
            time_spent,
            in_class
            AND (role = 'teacher')
        ) AS sum_time_spent_in_class_teacher,
        sumIfOrDefault(
            time_spent,
            in_class
            AND (role = 'administrator')
        ) AS sum_time_spent_in_class_administrator,
        uniqStateIf(sessionId, in_class) AS count_sessions_in_class,
        uniqStateIf(
            sessionId,
            in_class
            AND (role = 'student')
        ) AS count_sessions_in_class_student,
        uniqStateIf(
            sessionId,
            in_class
            AND (role = 'teacher')
        ) AS count_sessions_in_class_teacher,
        uniqStateIf(
            sessionId,
            in_class
            AND (role = 'administrator')
        ) AS count_sessions_in_class_administrator,
        quantileExactIfOrNull(0.5)(time_spent, NOT in_class) AS avg_time_spent_out_class,
        quantileExactIfOrNull(0.5)(
            time_spent,
            (NOT in_class)
            AND (role = 'student')
        ) AS avg_time_spent_out_class_student,
        quantileExactIfOrNull(0.5)(
            time_spent,
            (NOT in_class)
            AND (role = 'teacher')
        ) AS avg_time_spent_out_class_teacher,
        quantileExactIfOrNull(0.5)(
            time_spent,
            (NOT in_class)
            AND (role = 'administrator')
        ) AS avg_time_spent_out_class_administrator,
        sumIfOrDefault(time_spent, NOT in_class) AS sum_time_spent_out_class,
        sumIfOrDefault(
            time_spent,
            (NOT in_class)
            AND (role = 'student')
        ) AS sum_time_spent_out_class_student,
        sumIfOrDefault(
            time_spent,
            (NOT in_class)
            AND (role = 'teacher')
        ) AS sum_time_spent_out_class_teacher,
        sumIfOrDefault(
            time_spent,
            (NOT in_class)
            AND (role = 'administrator')
        ) AS sum_time_spent_out_class_administrator,
        uniqStateIf(sessionId, NOT in_class) AS count_sessions_out_class,
        uniqStateIf(
            sessionId,
            (NOT in_class)
            AND (role = 'student')
        ) AS count_sessions_out_class_student,
        uniqStateIf(
            sessionId,
            (NOT in_class)
            AND (role = 'teacher')
        ) AS count_sessions_out_class_teacher,
        uniqStateIf(
            sessionId,
            (NOT in_class)
            AND (role = 'administrator')
        ) AS count_sessions_out_class_administrator
    FROM session_metrics
    GROUP BY
        userId,
        schoolId,
        orgId,
        grade,        
        ${query.timeGranularity === 'all' ? '' : 'day,'} 
        ${query.productId ? 'productId' : 'domain'}
    `;
  }
}
