import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { <PERSON>QueryH<PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetUserData } from '@goteacher/app/analytics/query/data/get-user-data/query';
import { TimeGranularity } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

@QueryHandler(GetUserData)
export class GetUserDataHandler implements IQueryHandler<GetUserData> {
  private readonly logger = new Logger(GetUserDataHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserData): Promise<any> {
    const userOrganizationId = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    )[0];

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.ceil(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24)
        ) + 1
        : 1;

    const fromDateEpoch = query.fromDate ? Math.round(query.fromDate.getTime() / 1000) : 0;

    let toDateEpochExclusiveEndValue = 0;
    if (query.toDate) {
      const toDateExclusiveEnd = new Date(query.toDate);
      toDateExclusiveEnd.setDate(toDateExclusiveEnd.getDate() + 1);
      toDateEpochExclusiveEndValue = Math.round(toDateExclusiveEnd.getTime() / 1000);
    }

    const userSessionConditions = [];
    if (query.fromDate) {
      userSessionConditions.push(`act.startTime >= toDateTime(${fromDateEpoch})`);
    }
    if (query.toDate) {
      userSessionConditions.push(`act.startTime < toDateTime(${toDateEpochExclusiveEndValue})`);
    }
    const userSessionWhereClause = userSessionConditions.length > 0 ? `WHERE ${userSessionConditions.join(' AND ')}` : '';

    const orgActivityDateConditions = [];
    if (query.fromDate) {
      orgActivityDateConditions.push(`act_org.startTime >= toDateTime(${fromDateEpoch})`);
    }
    if (query.toDate) {
      orgActivityDateConditions.push(`act_org.startTime < toDateTime(${toDateEpochExclusiveEndValue})`);
    }
    const orgActivityDateFilterClause = orgActivityDateConditions.length > 0 ? `AND ${orgActivityDateConditions.join(' AND ')}` : '';

    const sqlQuery = `
      WITH user_info AS (
        SELECT userId, schoolId, orgId
        FROM goteacher.users u
        WHERE userId = '${query.userId}'
      ),
      user_sessions_cte AS (
        SELECT
          act.sessionId as sessionId,
          act.duration as session_duration,
          act.startTime as startTime
        FROM goteacher.activity_sessions as act
        INNER JOIN user_info ui ON act.userId = ui.userId
        ${userSessionWhereClause}
      ),
      user_overall_summary_cte AS (
        SELECT
          sum(session_duration) as sum_time_spent_period,
          avg(session_duration) as avg_session_duration_period,
          count(sessionId) as count_sessions_period
        FROM user_sessions_cte
      ),
      user_daily_activity_cte AS (
        SELECT
          toStartOfDay(startTime) as activity_day,
          sum(session_duration) as daily_total_seconds
        FROM user_sessions_cte
        GROUP BY
          activity_day
      ),
      user_daily_stats_cte AS (
        SELECT
          quantileExact(0.5)(uda.daily_total_seconds) AS median_daily_seconds,
          quantileExact(0.75)(uda.daily_total_seconds) AS p75_daily_seconds,
          (
            SELECT arrayAvg(
                      arraySlice(
                        arraySort(s_arr),
                        toUInt64(floor(length(s_arr) * 0.1)) + 1,
                        toUInt64(length(s_arr) - 2 * floor(length(s_arr) * 0.1))
                      )
                   )
            FROM (SELECT groupArray(daily_total_seconds) AS s_arr FROM user_daily_activity_cte WHERE daily_total_seconds > 0)
          ) AS trimmed_mean_daily_seconds
        FROM user_daily_activity_cte uda
        WHERE uda.daily_total_seconds > 0
      ),
      org_all_users_each_day_activity_cte AS (
        SELECT
          act_org.userId as userId,
          toStartOfDay(act_org.startTime) as activity_day, 
          sum(act_org.duration) as daily_total_seconds_for_user 
        FROM goteacher.activity_sessions as act_org
        INNER JOIN goteacher.users u ON act_org.userId = u.userId
        CROSS JOIN user_info target_user_details 
        WHERE u.orgId = target_user_details.orgId
          AND u.schoolId = target_user_details.schoolId 
          ${orgActivityDateFilterClause}
        GROUP BY act_org.userId, activity_day 
      ),
      org_users_median_daily_screen_time_cte AS (
        SELECT
          org_user_daily.userId as userId,
          quantileExact(0.5)(COALESCE(org_user_daily.daily_total_seconds_for_user, 0)) as median_daily_screen_time_seconds_for_org_user
        FROM org_all_users_each_day_activity_cte org_user_daily
        WHERE org_user_daily.daily_total_seconds_for_user > 0 
        GROUP BY org_user_daily.userId 
      ),
      ranked_org_users_cte AS (
        SELECT
          userId,
          median_daily_screen_time_seconds_for_org_user,
          rank() OVER (ORDER BY median_daily_screen_time_seconds_for_org_user DESC) as screen_time_median_rank,
          count() OVER () as total_users_ranked
        FROM org_users_median_daily_screen_time_cte
      ),
      organization_user_count_cte AS (
        SELECT
          count(DISTINCT u.userId) as total_users_in_comparison_group
        FROM goteacher.users u
        CROSS JOIN user_info target_user_details
        WHERE u.orgId = target_user_details.orgId
          AND u.schoolId = target_user_details.schoolId
      )
      SELECT
        ui_check.userId as userId,
        uos.sum_time_spent_period as sum_time_spent,
        uos.avg_session_duration_period as avg_session_duration,
        uos.count_sessions_period as count_sessions,
        (COALESCE(uds.median_daily_seconds, 0)) as median_daily_screen_time_seconds,
        (COALESCE(uds.p75_daily_seconds, 0)) as p75_daily_screen_time_seconds,
        (COALESCE(uds.trimmed_mean_daily_seconds, 0)) as trimmed_mean_daily_screen_time_seconds,
        (CASE 
            WHEN ${dateDiffInDays} > 14 THEN (COALESCE(uds.median_daily_seconds, 0)) * 7
         END) as median_weekly_screen_time_seconds_calc,
        COALESCE(r_org.screen_time_median_rank, 0) as screen_time_median_rank,
        COALESCE(r_org.total_users_ranked, 0) as total_users_ranked,
        COALESCE(ouc.total_users_in_comparison_group, 0) as total_users_in_comparison_group,
        COALESCE(r_org.median_daily_screen_time_seconds_for_org_user, 0) as user_median_seconds_for_ranking
      FROM user_info ui_check
      LEFT JOIN user_overall_summary_cte as uos ON 1=1
      LEFT JOIN user_daily_stats_cte as uds ON 1=1
      LEFT JOIN ranked_org_users_cte as r_org ON ui_check.userId = r_org.userId
      CROSS JOIN organization_user_count_cte as ouc
      WHERE ui_check.userId = '${query.userId}'
    `;

    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: any;
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      console.log('Returning cached data for GetUserData');
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    console.log('data', data);

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER,
    ]);

    await this.cacheService.set(
      cachingKey,
      { data: enrichedData },
      6 * 60 * 60, // 6 hours TTL
    );

    return { data: enrichedData };
  }
}
