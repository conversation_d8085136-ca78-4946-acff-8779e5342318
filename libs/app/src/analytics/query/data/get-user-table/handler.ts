import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetUserTableQuery } from '@goteacher/app/analytics/query/data/get-user-table/query';
import {
  EnrichmentColumn,
  EnrichmentService,
} from '@goteacher/app/analytics/service';
import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { School } from '@goteacher/app/models/sequelize/school.model';
import { User } from '@goteacher/app/models/sequelize/user.model';
import { UserSchool } from '@goteacher/app/models/sequelize/user.school.model';
import {
  isMoreThanTwoWeeks,
  PaginationResponse,
  parseOrderBy,
} from '@goteacher/app/utility';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';
import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';

@QueryHandler(GetUserTableQuery)
export class GetUserTableQueryHandler
  implements IQueryHandler<GetUserTableQuery> {
  private readonly logger = new Logger(GetUserTableQueryHandler.name);

  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
    private enrichmentService: EnrichmentService,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(School) private schoolModel: typeof School,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetUserTableQuery): Promise<any> {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const searchableUserIds = await this.getSearchableUserIds(
      userOrganizationIds,
      query,
    );

    const orderBy = parseOrderBy(
      query.order || [{ orderBy: 'sum_time_spent', orderDirection: 'desc' }],
    );

    const shouldAggregateAll =
      query.timeGranularity === TimeGranularity.ALL &&
      !query.fromDate &&
      !query.toDate;

    const shouldUseWeekly = query.fromDate
      ? isMoreThanTwoWeeks(query.fromDate, query.toDate)
      : false;

    const dateDiffInDays =
      query.fromDate && query.toDate
        ? Math.floor(
          (query.toDate.getTime() - query.fromDate.getTime()) /
          (1000 * 60 * 60 * 24),
        )
        : 1;

    console.log('dateDiffInDays', dateDiffInDays);

    const sqlQuery = `
      WITH user_daily_session_totals_cte AS (
        SELECT
          as_inner.userId as userId,
          toStartOfDay(as_inner.startTime) as activity_day, -- always daily for p75 calculation
          sum(as_inner.duration) as daily_total_seconds
        FROM goteacher.activity_sessions as as_inner
        LEFT JOIN goteacher.users u_inner on u_inner.userId = as_inner.userId
        WHERE
          u_inner.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.productId ? `AND as_inner.productId = '${query.productId}'` : ''}                  
          ${query.domain ? `AND as_inner.domain = '${query.domain}'` : ''}                  
          ${query.schoolIds?.length ? `AND u_inner.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u_inner.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as_inner.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as_inner.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u_inner.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u_inner.role = 'student'` : ''}
        GROUP BY
          as_inner.userId,
          activity_day
      ),
      user_p75_daily_cte AS (
        SELECT
          udst.userId as userId,
          quantileExact(0.5)(udst.daily_total_seconds) AS median_daily_seconds_for_user
        FROM user_daily_session_totals_cte udst
        WHERE udst.daily_total_seconds > 0
        GROUP BY
          udst.userId
      ),
      activity_sessions_cte AS (
        SELECT 
          ${query.productId ? 'productId,' : ''}          
          ${query.domain ? 'domain,' : ''}   
          sessionId,
          userId,          
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(startTime) AS day,' : 'toStartOfWeek(startTime) AS day,'} 
          sum(duration) as session_duration,          
          count(DISTINCT as.url) as page_views
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.productId ? `AND as.productId = '${query.productId}'` : ''}                  
          ${query.domain ? `AND as.domain = '${query.domain}'` : ''}                  
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) > 7 AND toHour(addHours(startTime, toInt32OrZero(clientTimezone))) < 15` : ''}
          ${searchableUserIds.length ? `AND as.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''}
        GROUP BY
          ${query.productId ? `as.productId,` : ''}          
          ${query.domain ? 'as.domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId,
          userId
      ), activity_sessions_summary_cte AS (
        SELECT 
          userId,
          ${query.productId ? 'productId,' : ''}          
          ${query.domain ? 'domain,' : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}

          sum(session_duration) as sum_duration,          
          avg(session_duration) as avg_duration,          
          count(sessionId) as count_sessions,
          uniqState(userId) as active_users,
          sum(page_views) as sum_page_views
        FROM activity_sessions_cte
        GROUP BY
          userId
          ${query.productId ? `,as.productId` : ''}          
          ${query.domain ? `,as.domain` : ''}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT
        ass.userId as userId,
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'ass.day as day,'}  
        ${query.productId ? 'ass.productId as productId,' : ''}        
        ${query.domain ? 'ass.domain as domain,' : ''}
        (COALESCE(up75.median_daily_seconds_for_user, 0)) as median_screen_time,
        sum(ass.sum_duration) as sum_time_spent,
        avg(ass.avg_duration) as avg_session_duration,
        avg(ass.avg_duration) as avg_time_spent,
        sum(ass.count_sessions) as count_sessions,
        sum(ass.sum_page_views) as page_views,
        uniqMerge(ass.active_users) as active_users,
        if(sum_time_spent < 60, 'inactive', 'active') as user_status,
        --CASE
        --  WHEN sessions.total_time_spent < 60 THEN 'inactive'
        --  ELSE 'active'
        --END AS user_status

        ${dateDiffInDays > 14
        ? `(COALESCE(up75.median_daily_seconds_for_user, 0)) * 7 as avg_screen_time`
        : `(COALESCE(up75.median_daily_seconds_for_user, 0)) as avg_screen_time`
      }
      FROM activity_sessions_summary_cte as ass
      LEFT JOIN user_p75_daily_cte up75 ON ass.userId = up75.userId
      GROUP BY
        userId,
        median_screen_time
        ${query.productId ? `, productId` : ''}
        ${query.domain ? `, domain` : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : `, day`}
      ORDER BY ${orderBy.map((item) => `${item[0]} ${item[1]}`).join(', ')} 
      LIMIT ${query.limit} 
      OFFSET ${query.offset};
    `;

    this.logger.debug(sqlQuery);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult =
      await this.cacheService.get<PaginationResponse<any>>(cachingKey);

    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data, rows_before_limit_at_least } = await queryResult.json();

    const enrichedData = await this.enrichmentService.enrich(data, [
      EnrichmentColumn.USER,
    ]);

    // const activeInactiveAggregation = enrichedData.reduce(
    //   (acc, user: any) => {
    //     if (user.user_status === 'active') {
    //       acc.active += 1;
    //     } else if (user.user_status === 'inactive') {
    //       acc.inactive += 1;
    //     }
    //     return acc;
    //   },
    //   { active: 0, inactive: 0 },
    // );

    // const totalUsersAggregation = enrichedData.reduce(
    //   (acc, user: any) => {
    //     const key = `${user.user_details.schoolId}-${user.user_details.grade}-${user.user_details.role}`;
    //     if (!acc[key]) {
    //       acc[key] = {
    //         schoolId: user.schoolId,
    //         grade: user.grade,
    //         role: user.role,
    //         totalUsers: 0,
    //       };
    //     }
    //     acc[key].totalUsers += 1;
    //     return acc;
    //   },
    //   {} as Record<
    //     string,
    //     { schoolId: string; grade: string; role: string; totalUsers: number }
    //   >,
    // );

    // const totalUsersBySchoolGradeRole = Object.values(totalUsersAggregation);

    const response: PaginationResponse<any> = {
      // data: [
      //   enrichedData,
      //   activeInactiveAggregation,
      //   totalUsersBySchoolGradeRole,
      // ],
      data: enrichedData,
      total: rows_before_limit_at_least,
      limit: query.limit,
      offset: query.offset,
    };
    await this.cacheService.set(cachingKey, response, 6 * 60 * 60);

    return response;
  }

  async getSearchableUserIds(
    orgIds: string[],
    query: GetUserTableQuery,
  ): Promise<string[]> {
    if (!query.search) return [];

    const schoolIds = await this.schoolModel.findAll({
      where: {
        organisationId: {
          [Op.in]: orgIds,
        },
      },
      attributes: ['id'],
    });

    const userIds = await this.userModel.findAll({
      where: {
        [Op.or]: [
          {
            email: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            firstName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
          {
            lastName: {
              [Op.iLike]: `%${query.search}%`,
            },
          },
        ],
      },
      attributes: ['id'],
      include: [
        {
          model: UserSchool,
          where: {
            schoolId: {
              [Op.in]: query.schoolIds?.length
                ? query.schoolIds
                : schoolIds.map((s) => s.id),
            },
          },
          include: [
            {
              model: School,
              where: {
                organisationId: {
                  [Op.in]: orgIds,
                },
              },
            },
          ],
        },
      ],
    });

    return userIds.map((u) => u.id);
  }

  getVisitsSQL(
    query: GetUserTableQuery,
    orgIds: string[],
    searchableUserIds: string[],
    shouldUseWeekly: boolean,
  ): string {
    const orgId = orgIds[0];
    return `
      SELECT 
      st.sessionId AS sessionId,
      st.tabId AS tabId,
      st.userId AS userId,
      COALESCE(u.orgId, st.orgId) AS orgId,
      COALESCE(u.schoolId, st.schoolId) AS schoolId,
      COALESCE(u.role, st.role) AS role,
      COALESCE(u.grade, st.grade) AS grade,
      ${query.productId ? 'st.productId AS productId,' : ''}
      ${query.domain ? 'st.domain AS domain,' : ''}
      st.url AS url,
      min(st.visit_start) AS visit_start,
      max(st.visit_end) AS visit_end,
      toStartOfDay(min(st.visit_start)) AS day,
      toStartOfWeek(min(st.visit_start)) AS week,
      any(st.visit_timezone) AS visit_timezone,
      dateDiff('s', visit_start, visit_end) AS time_spent,
      if(
        (
          toHour(
            addHours(visit_start, toInt32OrZero(visit_timezone))
          ) >= 7
        )
        AND (
          toHour(
            addHours(visit_start, toInt32OrZero(visit_timezone))
          ) <= 14
        ),
        1,
        0
      ) AS in_class
      FROM goteacher.visits_sc as st
      LEFT JOIN goteacher.users u on u.userId = st.userId
      WHERE 
      orgId = '${orgId}'
      ${query.productId ? `AND st.productId = '${query.productId}'` : ''}
      ${query.domain ? `AND st.domain = '${query.domain}'` : ''}            
      ${query.schoolIds?.length ? `AND schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
      ${query.grades?.length ? `AND grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
      ${query.userGroup === UserGroup.TEACHER ? `AND role = 'teacher'` : ''}
      ${query.userGroup === UserGroup.STUDENT ? `AND role = 'student'` : ''}
      ${query.fromDate ? (shouldUseWeekly ? `AND toDateTime(toStartOfWeek(st.visit_start)) >= ${Math.round(query.fromDate.getTime() / 1000)}` : `AND toStartOfDay(st.visit_start) >= ${Math.round(query.fromDate.getTime() / 1000)}`) : ''}
      ${query.toDate ? (shouldUseWeekly ? `AND toDateTime(toStartOfWeek(st.visit_start)) <= ${Math.round(query.toDate.getTime() / 1000)}` : `AND toStartOfDay(st.visit_start) <= ${Math.round(query.toDate.getTime() / 1000)}`) : ''}            
      ${searchableUserIds.length ? `AND st.userId IN (${searchableUserIds.map((sui) => `'${sui}'`).join(',')})` : ''} 
      GROUP BY
        sessionId,
        tabId,
        userId,
        orgId,
        schoolId,
        role,
        grade,
        ${query.productId ? 'productId,' : ''}
        ${query.domain ? 'domain,' : ''}
        url             
     `;
  }

  getDailySessionsSQL(query: GetUserTableQuery): string {
    return `
      SELECT 
        userId,
        schoolId,
        orgId,
        grade,        
        ${query.productId ? 'productId,' : ''}
        ${query.domain ? 'domain,' : ''}
        ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'day,' : 'toStartOfWeek(day) AS day,'} 
        avgOrNull(time_spent) AS avg_time_spent,        
        sumOrDefault(time_spent) AS sum_time_spent,        
        uniqState(sessionId) AS count_sessions,        
        avgIfOrNull(time_spent, in_class) AS avg_time_spent_in_class,        
        sumIfOrDefault(time_spent, in_class) AS sum_time_spent_in_class,        
        uniqStateIf(sessionId, in_class) AS count_sessions_in_class,        
        avgIfOrNull(time_spent, NOT in_class) AS avg_time_spent_out_class,        
        sumIfOrDefault(time_spent, NOT in_class) AS sum_time_spent_out_class,        
        uniqStateIf(sessionId, NOT in_class) AS count_sessions_out_class        
    FROM session_metrics
    GROUP BY
        userId,
        schoolId,
        orgId,        
        ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'} 
        ${query.productId ? 'productId,' : ''}
        ${query.domain ? 'domain,' : ''}
        grade
    `;
  }
}
