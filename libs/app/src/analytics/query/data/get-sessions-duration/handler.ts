import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { I<PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetSessionDurationsQuery } from '@goteacher/app/analytics/query/data/get-sessions-duration/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

export interface SessionsDuration {
  day?: Date | string;
  count_sessions_5: number;
  count_sessions_10: number;
  count_sessions_20: number;
  count_sessions_20_plus: number;
}

@QueryHandler(GetSessionDurationsQuery)
export class GetSessionDurationsQueryHandler
  implements IQueryHandler<GetSessionDurationsQuery> {
  private readonly logger = new Logger(GetSessionDurationsQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetSessionDurationsQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const sqlQuery = `
      SELECT
        ${query.timeGranularity !== TimeGranularity.ALL ? query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(ism.startTime) AS day,' : 'toStartOfWeek(ism.startTime) AS day,' : ''}

        countIf(ism.duration <= 300) AS count_sessions_5,
        countIf(ism.duration > 300 AND ism.duration <= 600) AS count_sessions_10,
        countIf(ism.duration > 600 AND ism.duration <= 1200) AS count_sessions_20,
        countIf(ism.duration > 1200) AS count_sessions_20_plus
      FROM goteacher.interaction_summaries_local ism
      WHERE
        ism.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
        ${query.productId ? `AND ism.productId = '${query.productId}'` : `AND ism.domain = '${query.domain}'`}
        ${query.schoolIds?.length ? `AND ism.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.grades?.length ? `AND ism.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
        ${query.fromDate ? `AND ism.startTime >= '${query.fromDate.toISOString()}'` : ''}
        ${query.toDate ? `AND ism.startTime <= '${query.toDate.toISOString()}'` : ''}
        ${query.userGroup === UserGroup.TEACHER ? `AND ism.role = 'teacher'` : ''}
        ${query.userGroup === UserGroup.STUDENT ? `AND ism.role = 'student'` : ''}
        ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) > 7 AND toHour(addHours(ism.startTime, toInt32OrZero(ism.clientTimezone))) < 14` : ''}
      ${query.timeGranularity !== TimeGranularity.ALL ? `GROUP BY day` : ''}
      ${query.timeGranularity !== TimeGranularity.ALL ? `ORDER BY day ASCENDING` : ''}
      ;
    `;

    this.logger.debug(`sqlQuery: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: SessionsDuration[];
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();

    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data: data };
  }
}
