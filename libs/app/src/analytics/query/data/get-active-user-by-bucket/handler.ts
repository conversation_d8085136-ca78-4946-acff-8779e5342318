import { TimeGranularity, UserGroup } from '@goteacher/app/analytics/types';
import { I<PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';

import { NodeClickHouseClient } from '@clickhouse/client/dist/client';
import { GetActiveUsersBucketQuery } from '@goteacher/app/analytics/query/data/get-active-user-by-bucket/query';
import { NormalizeDateRange } from '@goteacher/app/common/decorators/normalize-data-range.decorator';
import { ICacheService } from '@goteacher/infra/cache';
import { Logger } from '@nestjs/common';

export interface ActiveUserByBucketResponse {
  orgId: string;
  domain: string;
  day?: Date | string;
  uniq_user_spent_20: number;
  uniq_user_spent_40: number;
  uniq_user_spent_60: number;
  uniq_user_spent_120: number;
  uniq_user_spent_120_plus: number;
  no_login: number;
}

@QueryHandler(GetActiveUsersBucketQuery)
export class GetActiveUsersBucketQueryHandler
  implements IQueryHandler<GetActiveUsersBucketQuery> {
  private readonly logger = new Logger(GetActiveUsersBucketQueryHandler.name);
  constructor(
    private cacheService: ICacheService,
    private clickhouse: NodeClickHouseClient,
  ) { }

  @NormalizeDateRange()
  async execute(query: GetActiveUsersBucketQuery) {
    const userOrganizationIds = query.ctx.user.UserSchool.map(
      (userSchool) => userSchool.school.organisationId,
    );

    const sqlQuery = `
      WITH total_users as (
        SELECT  u.orgId as orgId,
          count(*) as cu
        FROM 
          users u
        WHERE 
          u.orgId in (${userOrganizationIds.map((o) => `'${o}'`).join(',')})
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
        GROUP BY orgId
      ), activity_sessions_cte AS (
        SELECT
          u.orgId as orgId,
          u.userId as userId,
          ${query.productId ? `as.productId AS productId,` : 'as.domain AS domain,'}
          as.sessionId as sessionId,
          ${query.timeGranularity === TimeGranularity.ALL ? '' : query.timeGranularity === TimeGranularity.DAILY ? 'toStartOfDay(as.startTime) AS day,' : 'toStartOfWeek(as.startTime) AS day,'} 
          sum(as.duration) as session_duration          
        FROM goteacher.activity_sessions as as
        LEFT JOIN goteacher.users u on u.userId = as.userId
        WHERE
          u.orgId IN (${userOrganizationIds.map((o) => `'${o}'`).join(',')})          
          ${query.productId ? `AND as.productId = '${query.productId}'` : `AND as.domain = '${query.domain}'`}                  
          ${query.schoolIds?.length ? `AND u.schoolId IN (${query.schoolIds.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.grades?.length ? `AND u.grade IN (${query.grades.map((s) => `'${s}'`).join(',')})` : ''}
          ${query.fromDate ? `AND as.startTime >= ${Math.round(query.fromDate.getTime() / 1000)}` : ''}
          ${query.toDate ? `AND as.startTime <= ${Math.round(query.toDate.getTime() / 1000)}` : ''}
          ${query.userGroup === UserGroup.TEACHER ? `AND u.role = 'teacher'` : ''}
          ${query.userGroup === UserGroup.STUDENT ? `AND u.role = 'student'` : ''}
          ${userOrganizationIds[0] === '9afaf988-f75a-4458-a8ae-020327e4793e' ? `AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) > 7 AND toHour(addHours(as.startTime, toInt32OrZero(as.clientTimezone))) < 14` : ''}
        GROUP BY
          u.orgId,
          u.userId,
          ${query.productId ? `as.productId,` : 'as.domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sessionId
      ), activity_sessions_summary_cte AS (
        SELECT
          orgId,
          userId,
          ${query.productId ? `productId AS productId,` : 'domain AS domain,'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : 'day,'}
          sum(session_duration) as sum_duration,
          avg(session_duration) as avg_duration,
          count(sessionId) as count_sessions
        FROM activity_sessions_cte
        GROUP BY
          orgId,
          userId,
          ${query.productId ? `productId` : 'domain'}
          ${query.timeGranularity === TimeGranularity.ALL ? '' : ',day'}
      )
      SELECT 
        ass.orgId as orgId,
        ${query.productId ? `ass.productId as productId,` : 'ass.domain as domain,'}
        ${query.timeGranularity !== TimeGranularity.ALL ? `ass.day as day,` : ''} 
        uniqIf(
          ass.userId, 
          ass.sum_duration <= 20 * 60
        ) as uniq_user_spent_20,
        uniqIf(
          ass.userId,
          ass.sum_duration > 20 * 60 AND ass.sum_duration <= 40 * 60
        ) as uniq_user_spent_40,
        uniqIf(
          ass.userId,
          ass.sum_duration > 40 * 60 AND ass.sum_duration <= 60 * 60
        ) as uniq_user_spent_60,
        uniqIf(
          ass.userId,
          ass.sum_duration > 60 * 60 AND ass.sum_duration <= 120 * 60
        ) as uniq_user_spent_120,
        uniqIf(
          ass.userId,
          ass.sum_duration > 120 * 60
        ) as uniq_user_spent_120_plus,
        tu.cu - (
          uniq_user_spent_20 + uniq_user_spent_40 + uniq_user_spent_60 + uniq_user_spent_120 + uniq_user_spent_120_plus
        ) as no_login
      FROM activity_sessions_summary_cte ass
      INNER JOIN total_users tu ON tu.orgId = ass.orgId
      GROUP BY
        ass.orgId,
        ${query.productId ? `ass.productId,` : 'ass.domain,'}
        ${query.timeGranularity !== TimeGranularity.ALL ? `ass.day,` : ''}
        tu.cu
      ${query.timeGranularity !== TimeGranularity.ALL ? `ORDER BY ass.day ASCENDING` : ''}
    `;

    this.logger.debug(`sqlQuery active-user-by-bucket: ${sqlQuery}`);

    const cachingKey = this.cacheService.genKey(sqlQuery);
    const cacheResult = await this.cacheService.get<{
      data: ActiveUserByBucketResponse[];
    }>(cachingKey);
    if (cacheResult && !query.forceRefresh) {
      return cacheResult;
    }

    const queryResult = await this.clickhouse.query({
      query: sqlQuery,
      format: 'JSON',
    });
    const { data } = await queryResult.json();
    await this.cacheService.set(cachingKey, { data: data }, 6 * 60 * 60);

    return { data };
  }
}
