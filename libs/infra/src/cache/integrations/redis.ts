import { ICacheService } from '@goteacher/infra/cache/cache.service';
import {
  Injectable,
  <PERSON><PERSON>,
  OnModuleDestroy,
  OnModuleInit,
} from '@nestjs/common';
import crypto from 'crypto';
import { RedisClientOptions, createClient } from 'redis';

@Injectable()
export class RedisService
  implements ICacheService, OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(RedisService.name);
  private client;
  private clientOptions: RedisClientOptions;
  constructor(clientOptions: RedisClientOptions) {
    this.clientOptions = clientOptions;
  }

  public genKey(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  async onModuleInit() {
    this.client = createClient(this.clientOptions);
    await this.client.connect();
    this.logger.log('Redis connected');
  }

  async onModuleDestroy() {
    this.logger.log('Disconnecting Redis...');
    if (this.client) this.client.disconnect();
    this.logger.log('Disconnected from redis!');
  }

  async set<T>(key: string, value: T, ttl?: number) {
    this.logger.debug(`Setting key ${key} with value ${value}`);
    await this.client.set(key, JSON.stringify(value), { EX: ttl });
  }

  async get<T>(key: string) {
    const data = await this.client.get(key);
    return JSON.parse(data) as T;
  }

  async del(key: string) {
    await this.client.del(key);
  }

  async invalidate(pattern: string) {
    const keysToDelete = [];
    for await (const key of this.client.scanIterator({
      MATCH: pattern,
      COUNT: 100,
    })) {
      keysToDelete.push(key);
    }
    if (keysToDelete.length > 0) {
      await this.client.del(keysToDelete);
      this.logger.debug(`Invalidated ${keysToDelete.length} keys`);
    }
  }
}
