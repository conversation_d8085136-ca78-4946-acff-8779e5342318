import { ConsumerConfig, KafkaConfig, ProducerConfig } from "kafkajs";

export type KafkaForRootArguments = KafkaForRootArgument[];

export interface KafkaForRootArgument {
  name: string;
  useFactory: (...args: any[]) => KafkaConfig;
  imports?: any[];
  inject?: any[];
}

export type KafkaRegisterArguments = KafkaServiceArguments[];
export type Topic = string;

export interface KafkaServiceArguments {
  name: string;
  connName: string;
  consumers: KafkaConsumerConfig[];
  producer: KafkaProducerConfig;
}

export interface KafkaConsumerConfig {
  options: ConsumerConfig & { groupId: string };
  count?: number;
}

export interface KafkaProducerConfig {
  options: ProducerConfig;
  topics: Topic[];
}

export type EventControllerMetadata = string;
export type EventListenerMetadata = {
  topic: Topic;
  groupId: string;
  handler: any;
  dlq: Topic;
}[];

export type DiscoveryTarget = {
  topic: Topic;
  clientName: string;
  groupId: string;
  handlers: { target: any; handler: any; dlq: Topic }[];
};
