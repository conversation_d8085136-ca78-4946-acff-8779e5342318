import { CompleteMultipartUploadCommandOutput } from '@aws-sdk/client-s3';
import { Storage } from '@google-cloud/storage';
import { IStorageService } from '@goteacher/infra/storage/storage.service';
import {
  GetReadRequest,
  GetWriteRequest,
  MultiPartChunkRequest,
  MultiPartEndRequest,
  MultiPartStartRequest,
} from '@goteacher/infra/storage/storage.types';
import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class GCSService implements IStorageService {
  public client: Storage;

  constructor() {
    this.initClient();
  }

  private initClient() {
    this.client = new Storage();
  }

  public async duplicateObject(
    destinationBucket: string,
    objectKeyPath: string,
    newObjectKey: string,
  ): Promise<void> {
    const [bucket] = destinationBucket.split('/');
    const sourceBucket = this.client.bucket(bucket);
    const file = sourceBucket.file(objectKeyPath);
    
    await file.copy(sourceBucket.file(newObjectKey));
  }

  public async initiateMultiPart(_args: MultiPartStartRequest): Promise<string> {
    throw new Error('Multipart upload not implemented for GCS');
  }

  public async getMultiPartChunkURL(
    _args: MultiPartChunkRequest,
  ): Promise<{ url: string }> {
    throw new Error('Multipart upload not implemented for GCS');
  }

  public async completeMultiPart(
    _args: MultiPartEndRequest,
  ): Promise<CompleteMultipartUploadCommandOutput> {
    throw new Error('Multipart upload not implemented for GCS');
  }

  public async getReadUrl({
    target,
    filename,
    expiresInMinutes = 15,
  }: GetReadRequest): Promise<{ url: string }> {
    const bucket = this.client.bucket(target.container);
    const file = bucket.file(target.path);

    const [url] = await file.getSignedUrl({
      version: 'v4',
      action: 'read',
      expires: Date.now() + expiresInMinutes * 60 * 1000,
      ...(filename ? { 
        responseDisposition: `attachment; filename="${filename}"` 
      } : {}),
    });

    return { url };
  }

  public async getWriteUrl({
    target,
    expiresInMinutes = 15,
  }: GetWriteRequest): Promise<{ url: string; path: string }> {
    const bucket = this.client.bucket(target.container);
    const file = bucket.file(target.path);

    const [url] = await file.getSignedUrl({
      version: 'v4',
      action: 'write',
      expires: Date.now() + expiresInMinutes * 60 * 1000,
    });

    return { url, path: target.path };
  }

  public async getObjectAsString(
    container: string,
    path: string,
  ): Promise<string> {
    const bucket = this.client.bucket(container);
    const file = bucket.file(path);
    const [contents] = await file.download();
    
    return contents.toString('utf-8');
  }

  public async getObjectAsWebStream(
    container: string,
    path: string,
  ): Promise<ReadableStream> {
    const bucket = this.client.bucket(container);
    const file = bucket.file(path);
    const stream = file.createReadStream();
    
    return stream as unknown as ReadableStream;
  }

  public async getObjectAsByteArray(
    container: string,
    path: string,
  ): Promise<Uint8Array> {
    const bucket = this.client.bucket(container);
    const file = bucket.file(path);
    const [contents] = await file.download();
    
    return new Uint8Array(contents);
  }

  public async writeObject(
    container: string,
    path: string,
    data: string | Buffer,
  ): Promise<void> {
    const bucket = this.client.bucket(container);
    const file = bucket.file(path);
    
    await file.save(data);
  }

  public async getChecksum(
    container: string,
    path: string,
  ): Promise<string | null> {
    const bucket = this.client.bucket(container);
    const file = bucket.file(path);
    const [metadata] = await file.getMetadata();
    
    return metadata.etag || null;
  }

  /**
   * Fetch GCP access token from metadata service
   */
  public async getGCPAccessToken(): Promise<string> {
    try {
      const response = await axios.get(
        'http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token',
        {
          headers: {
            'Metadata-Flavor': 'Google'
          },
          timeout: 5000
        }
      );
      return response.data.access_token;
    } catch (error) {
      throw new Error(error?.message);
    }
  }

  /**
   * Download file from GCS URL using token-based authentication
   */
  public async downloadFileFromGCSUrlWithToken(url: string): Promise<Buffer> {
    try {
      const accessToken = await this.getGCPAccessToken();
      
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        timeout: 30000,
        maxContentLength: 50 * 1024 * 1024,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'text/csv, application/csv, text/plain'
        }
      });

      return Buffer.from(response.data);
    } catch (error) {
      console.error('Failed to download from GCS with token:', error);
      throw new Error(`Failed to download from GCS: ${error.message}`);
    }
  }

  public async downloadFileFromGCSUrl(url: string): Promise<Buffer> {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.substring(1).split('/');
    const bucketName = pathParts[0];
    const filePath = pathParts.slice(1).join('/');

    const bucket = this.client.bucket(bucketName);
    const file = bucket.file(filePath);
    
    const [exists] = await file.exists();
    if (!exists) {
      throw new Error(`File not found: ${url}`);
    }

    const [contents] = await file.download();
    return contents;
  }
}