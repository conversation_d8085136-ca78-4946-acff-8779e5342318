{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}, "monorepo": true, "root": "apps/api", "projects": {"api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "seeds": {"type": "application", "root": "apps/seeds", "entryFile": "main", "sourceRoot": "apps/seeds/src", "compilerOptions": {"tsConfigPath": "apps/seeds/tsconfig.app.json"}}, "app": {"type": "library", "root": "libs/app", "entryFile": "index", "sourceRoot": "libs/app/src", "compilerOptions": {"tsConfigPath": "libs/app/tsconfig.lib.json"}}, "infra": {"type": "library", "root": "libs/infra", "entryFile": "index", "sourceRoot": "libs/infra/src", "compilerOptions": {"tsConfigPath": "libs/infra/tsconfig.lib.json"}}, "export": {"type": "application", "root": "apps/export", "entryFile": "main", "sourceRoot": "apps/export/src", "compilerOptions": {"tsConfigPath": "apps/export/tsconfig.app.json"}}, "ingestion": {"type": "application", "root": "apps/ingestion", "entryFile": "main", "sourceRoot": "apps/ingestion/src", "compilerOptions": {"tsConfigPath": "apps/ingestion/tsconfig.app.json"}}, "enrichment_worker": {"type": "application", "root": "apps/enrichment_worker", "entryFile": "main", "sourceRoot": "apps/enrichment_worker/src", "compilerOptions": {"tsConfigPath": "apps/enrichment_worker/tsconfig.app.json"}}}}